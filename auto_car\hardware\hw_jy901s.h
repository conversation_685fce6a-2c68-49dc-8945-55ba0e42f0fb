#ifndef _HW_JY901S_H_
#define _HW_JY901S_H_

#include "ti_msp_dl_config.h"

extern float target_angle;

// #define delay_us(__us)  delay_cycles((CPUCLK_FREQ/1000000)*__us)
// #define delay_ms(__ms)  delay_cycles((CPUCLK_FREQ/1000)*__ms)


/* BNO080 配置参数 */
#define BNO080_RX_BUFFER_SIZE  128 
#define RVC_PACKET_SIZE         19
#define RVC_HEADER              0xAAAA

typedef struct {
    float yaw; float pitch; float roll;
    volatile uint8_t new_data_flag;
} BNO080_Data_t;





void BNO080_GetData(BNO080_Data_t* data_ptr);

void uart3_init(void);




#endif

