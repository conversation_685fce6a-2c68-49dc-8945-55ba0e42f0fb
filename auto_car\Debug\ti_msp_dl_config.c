/*
 * Copyright (c) 2023, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.c =============
 *  Configured MSPM0 DriverLib module definitions
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */

#include "ti_msp_dl_config.h"

DL_TimerA_backupConfig gPWM_MOTORRBackup;
DL_TimerG_backupConfig gPWM_BEEPBackup;
DL_TimerA_backupConfig gTIMER_TICKBackup;
DL_UART_Main_backupConfig gUART_BNO080Backup;

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform any initialization needed before using any board APIs
 */
SYSCONFIG_WEAK void SYSCFG_DL_init(void)
{
    SYSCFG_DL_initPower();
    SYSCFG_DL_GPIO_init();
    /* Module-Specific Initializations*/
    SYSCFG_DL_SYSCTL_init();
    SYSCFG_DL_PWM_MOTORL_init();
    SYSCFG_DL_PWM_MOTORR_init();
    SYSCFG_DL_PWM_BEEP_init();
    SYSCFG_DL_SPARE_PWM_init();
    SYSCFG_DL_TIMER_TICK_init();
    SYSCFG_DL_SERVO_IIC_init();
    SYSCFG_DL_OLED_UART_init();
    SYSCFG_DL_UART_0_init();
    SYSCFG_DL_UART_BNO080_init();
    SYSCFG_DL_POWER_ADC_init();
    SYSCFG_DL_SYSTICK_init();
    /* Ensure backup structures have no valid state */
	gPWM_MOTORRBackup.backupRdy 	= false;
	gPWM_BEEPBackup.backupRdy 	= false;
	gTIMER_TICKBackup.backupRdy 	= false;
	gUART_BNO080Backup.backupRdy 	= false;

}
/*
 * User should take care to save and restore register configuration in application.
 * See Retention Configuration section for more details.
 */
SYSCONFIG_WEAK bool SYSCFG_DL_saveConfiguration(void)
{
    bool retStatus = true;

	retStatus &= DL_TimerA_saveConfiguration(PWM_MOTORR_INST, &gPWM_MOTORRBackup);
	retStatus &= DL_TimerG_saveConfiguration(PWM_BEEP_INST, &gPWM_BEEPBackup);
	retStatus &= DL_TimerA_saveConfiguration(TIMER_TICK_INST, &gTIMER_TICKBackup);
	retStatus &= DL_UART_Main_saveConfiguration(UART_BNO080_INST, &gUART_BNO080Backup);

    return retStatus;
}


SYSCONFIG_WEAK bool SYSCFG_DL_restoreConfiguration(void)
{
    bool retStatus = true;

	retStatus &= DL_TimerA_restoreConfiguration(PWM_MOTORR_INST, &gPWM_MOTORRBackup, false);
	retStatus &= DL_TimerG_restoreConfiguration(PWM_BEEP_INST, &gPWM_BEEPBackup, false);
	retStatus &= DL_TimerA_restoreConfiguration(TIMER_TICK_INST, &gTIMER_TICKBackup, false);
	retStatus &= DL_UART_Main_restoreConfiguration(UART_BNO080_INST, &gUART_BNO080Backup);

    return retStatus;
}

SYSCONFIG_WEAK void SYSCFG_DL_initPower(void)
{
    DL_GPIO_reset(GPIOA);
    DL_GPIO_reset(GPIOB);
    DL_TimerG_reset(PWM_MOTORL_INST);
    DL_TimerA_reset(PWM_MOTORR_INST);
    DL_TimerG_reset(PWM_BEEP_INST);
    DL_TimerG_reset(SPARE_PWM_INST);
    DL_TimerA_reset(TIMER_TICK_INST);
    DL_I2C_reset(SERVO_IIC_INST);
    DL_UART_Main_reset(OLED_UART_INST);
    DL_UART_Main_reset(UART_0_INST);
    DL_UART_Main_reset(UART_BNO080_INST);
    DL_ADC12_reset(POWER_ADC_INST);


    DL_GPIO_enablePower(GPIOA);
    DL_GPIO_enablePower(GPIOB);
    DL_TimerG_enablePower(PWM_MOTORL_INST);
    DL_TimerA_enablePower(PWM_MOTORR_INST);
    DL_TimerG_enablePower(PWM_BEEP_INST);
    DL_TimerG_enablePower(SPARE_PWM_INST);
    DL_TimerA_enablePower(TIMER_TICK_INST);
    DL_I2C_enablePower(SERVO_IIC_INST);
    DL_UART_Main_enablePower(OLED_UART_INST);
    DL_UART_Main_enablePower(UART_0_INST);
    DL_UART_Main_enablePower(UART_BNO080_INST);
    DL_ADC12_enablePower(POWER_ADC_INST);

    delay_cycles(POWER_STARTUP_DELAY);
}

SYSCONFIG_WEAK void SYSCFG_DL_GPIO_init(void)
{

    DL_GPIO_initPeripheralAnalogFunction(GPIO_HFXIN_IOMUX);
    DL_GPIO_initPeripheralAnalogFunction(GPIO_HFXOUT_IOMUX);

    DL_GPIO_initPeripheralOutputFunction(GPIO_PWM_MOTORL_C0_IOMUX,GPIO_PWM_MOTORL_C0_IOMUX_FUNC);
    DL_GPIO_enableOutput(GPIO_PWM_MOTORL_C0_PORT, GPIO_PWM_MOTORL_C0_PIN);
    DL_GPIO_initPeripheralOutputFunction(GPIO_PWM_MOTORL_C1_IOMUX,GPIO_PWM_MOTORL_C1_IOMUX_FUNC);
    DL_GPIO_enableOutput(GPIO_PWM_MOTORL_C1_PORT, GPIO_PWM_MOTORL_C1_PIN);
    DL_GPIO_initPeripheralOutputFunction(GPIO_PWM_MOTORR_C0_IOMUX,GPIO_PWM_MOTORR_C0_IOMUX_FUNC);
    DL_GPIO_enableOutput(GPIO_PWM_MOTORR_C0_PORT, GPIO_PWM_MOTORR_C0_PIN);
    DL_GPIO_initPeripheralOutputFunction(GPIO_PWM_MOTORR_C1_IOMUX,GPIO_PWM_MOTORR_C1_IOMUX_FUNC);
    DL_GPIO_enableOutput(GPIO_PWM_MOTORR_C1_PORT, GPIO_PWM_MOTORR_C1_PIN);
    DL_GPIO_initPeripheralOutputFunction(GPIO_PWM_BEEP_C0_IOMUX,GPIO_PWM_BEEP_C0_IOMUX_FUNC);
    DL_GPIO_enableOutput(GPIO_PWM_BEEP_C0_PORT, GPIO_PWM_BEEP_C0_PIN);
    DL_GPIO_initPeripheralOutputFunction(GPIO_SPARE_PWM_C0_IOMUX,GPIO_SPARE_PWM_C0_IOMUX_FUNC);
    DL_GPIO_enableOutput(GPIO_SPARE_PWM_C0_PORT, GPIO_SPARE_PWM_C0_PIN);
    DL_GPIO_initPeripheralOutputFunction(GPIO_SPARE_PWM_C1_IOMUX,GPIO_SPARE_PWM_C1_IOMUX_FUNC);
    DL_GPIO_enableOutput(GPIO_SPARE_PWM_C1_PORT, GPIO_SPARE_PWM_C1_PIN);

    DL_GPIO_initPeripheralInputFunctionFeatures(GPIO_SERVO_IIC_IOMUX_SDA,
        GPIO_SERVO_IIC_IOMUX_SDA_FUNC, DL_GPIO_INVERSION_DISABLE,
        DL_GPIO_RESISTOR_NONE, DL_GPIO_HYSTERESIS_DISABLE,
        DL_GPIO_WAKEUP_DISABLE);
    DL_GPIO_initPeripheralInputFunctionFeatures(GPIO_SERVO_IIC_IOMUX_SCL,
        GPIO_SERVO_IIC_IOMUX_SCL_FUNC, DL_GPIO_INVERSION_DISABLE,
        DL_GPIO_RESISTOR_NONE, DL_GPIO_HYSTERESIS_DISABLE,
        DL_GPIO_WAKEUP_DISABLE);
    DL_GPIO_enableHiZ(GPIO_SERVO_IIC_IOMUX_SDA);
    DL_GPIO_enableHiZ(GPIO_SERVO_IIC_IOMUX_SCL);

    DL_GPIO_initPeripheralOutputFunction(
        GPIO_OLED_UART_IOMUX_TX, GPIO_OLED_UART_IOMUX_TX_FUNC);
    DL_GPIO_initPeripheralInputFunction(
        GPIO_OLED_UART_IOMUX_RX, GPIO_OLED_UART_IOMUX_RX_FUNC);
    DL_GPIO_initPeripheralOutputFunction(
        GPIO_UART_0_IOMUX_TX, GPIO_UART_0_IOMUX_TX_FUNC);
    DL_GPIO_initPeripheralInputFunction(
        GPIO_UART_0_IOMUX_RX, GPIO_UART_0_IOMUX_RX_FUNC);
    DL_GPIO_initPeripheralOutputFunction(
        GPIO_UART_BNO080_IOMUX_TX, GPIO_UART_BNO080_IOMUX_TX_FUNC);
    DL_GPIO_initPeripheralInputFunction(
        GPIO_UART_BNO080_IOMUX_RX, GPIO_UART_BNO080_IOMUX_RX_FUNC);

    DL_GPIO_initDigitalOutput(LED_LED_PIN_IOMUX);

    DL_GPIO_initDigitalInputFeatures(GPIO_ENCODER_PIN_LA_IOMUX,
		 DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_NONE,
		 DL_GPIO_HYSTERESIS_DISABLE, DL_GPIO_WAKEUP_DISABLE);

    DL_GPIO_initDigitalInputFeatures(GPIO_ENCODER_PIN_LB_IOMUX,
		 DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_NONE,
		 DL_GPIO_HYSTERESIS_DISABLE, DL_GPIO_WAKEUP_DISABLE);

    DL_GPIO_initDigitalInputFeatures(GPIO_ENCODER_PIN_RA_IOMUX,
		 DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_NONE,
		 DL_GPIO_HYSTERESIS_DISABLE, DL_GPIO_WAKEUP_DISABLE);

    DL_GPIO_initDigitalInputFeatures(GPIO_ENCODER_PIN_RB_IOMUX,
		 DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_NONE,
		 DL_GPIO_HYSTERESIS_DISABLE, DL_GPIO_WAKEUP_DISABLE);

    DL_GPIO_initDigitalInputFeatures(BLE_BLE_STATE_IOMUX,
		 DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_NONE,
		 DL_GPIO_HYSTERESIS_DISABLE, DL_GPIO_WAKEUP_DISABLE);

    DL_GPIO_initDigitalOutput(BLE_BLE_EN_IOMUX);

    DL_GPIO_initDigitalInputFeatures(KEY_KEY_CENTER_IOMUX,
		 DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_PULL_UP,
		 DL_GPIO_HYSTERESIS_DISABLE, DL_GPIO_WAKEUP_DISABLE);

    DL_GPIO_initDigitalInputFeatures(KEY_KEY_DOWN_IOMUX,
		 DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_PULL_UP,
		 DL_GPIO_HYSTERESIS_DISABLE, DL_GPIO_WAKEUP_DISABLE);

    DL_GPIO_initDigitalInputFeatures(KEY_KEY_LEFT_IOMUX,
		 DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_PULL_UP,
		 DL_GPIO_HYSTERESIS_DISABLE, DL_GPIO_WAKEUP_DISABLE);

    DL_GPIO_initDigitalInputFeatures(KEY_KEY_UP_IOMUX,
		 DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_PULL_UP,
		 DL_GPIO_HYSTERESIS_DISABLE, DL_GPIO_WAKEUP_DISABLE);

    DL_GPIO_initDigitalOutput(RANGING_IIC_RANGING_SDA_IOMUX);

    DL_GPIO_initDigitalOutput(RANGING_IIC_RANGING_SCL_IOMUX);

    DL_GPIO_initDigitalOutput(EXTEND_IIC_EXTEND_SCL_IOMUX);

    DL_GPIO_initDigitalOutput(EXTEND_IIC_EXTEND_SDA_IOMUX);

    DL_GPIO_initDigitalOutput(OLED_IIC_OLED_SDA_IOMUX);

    DL_GPIO_initDigitalOutput(OLED_IIC_OLED_SCL_IOMUX);

    DL_GPIO_initDigitalOutput(GPIO_GRAY_SERIAL_PIN_CLK_IOMUX);

    DL_GPIO_initDigitalInput(GPIO_GRAY_SERIAL_PIN_FOWARD_DAT_IOMUX);

    DL_GPIO_initDigitalOutput(GYRO_IIC_GYRO_SDA_IOMUX);

    DL_GPIO_initDigitalOutput(GYRO_IIC_GYRO_SCL_IOMUX);

    DL_GPIO_initDigitalOutput(GYRO_IIC_NRST_IOMUX);

    DL_GPIO_clearPins(GPIOA, GPIO_GRAY_SERIAL_PIN_CLK_PIN |
		GYRO_IIC_GYRO_SDA_PIN |
		GYRO_IIC_GYRO_SCL_PIN);
    DL_GPIO_setPins(GPIOA, RANGING_IIC_RANGING_SDA_PIN |
		RANGING_IIC_RANGING_SCL_PIN |
		EXTEND_IIC_EXTEND_SCL_PIN |
		EXTEND_IIC_EXTEND_SDA_PIN |
		GYRO_IIC_NRST_PIN);
    DL_GPIO_enableOutput(GPIOA, RANGING_IIC_RANGING_SDA_PIN |
		RANGING_IIC_RANGING_SCL_PIN |
		EXTEND_IIC_EXTEND_SCL_PIN |
		EXTEND_IIC_EXTEND_SDA_PIN |
		GPIO_GRAY_SERIAL_PIN_CLK_PIN |
		GYRO_IIC_GYRO_SDA_PIN |
		GYRO_IIC_GYRO_SCL_PIN |
		GYRO_IIC_NRST_PIN);
    DL_GPIO_setLowerPinsPolarity(GPIOA, DL_GPIO_PIN_12_EDGE_RISE);
    DL_GPIO_clearInterruptStatus(GPIOA, GPIO_ENCODER_PIN_RA_PIN);
    DL_GPIO_enableInterrupt(GPIOA, GPIO_ENCODER_PIN_RA_PIN);
    DL_GPIO_clearPins(GPIOB, LED_LED_PIN_PIN |
		OLED_IIC_OLED_SCL_PIN);
    DL_GPIO_setPins(GPIOB, BLE_BLE_EN_PIN |
		OLED_IIC_OLED_SDA_PIN);
    DL_GPIO_enableOutput(GPIOB, LED_LED_PIN_PIN |
		BLE_BLE_EN_PIN |
		OLED_IIC_OLED_SDA_PIN |
		OLED_IIC_OLED_SCL_PIN);
    DL_GPIO_setLowerPinsPolarity(GPIOB, DL_GPIO_PIN_6_EDGE_RISE);
    DL_GPIO_setUpperPinsPolarity(GPIOB, DL_GPIO_PIN_23_EDGE_RISE |
		DL_GPIO_PIN_27_EDGE_RISE);
    DL_GPIO_clearInterruptStatus(GPIOB, GPIO_ENCODER_PIN_LA_PIN);
    DL_GPIO_enableInterrupt(GPIOB, GPIO_ENCODER_PIN_LA_PIN);

}


static const DL_SYSCTL_SYSPLLConfig gSYSPLLConfig = {
    .inputFreq              = DL_SYSCTL_SYSPLL_INPUT_FREQ_32_48_MHZ,
	.rDivClk2x              = 1,
	.rDivClk1               = 0,
	.rDivClk0               = 0,
	.enableCLK2x            = DL_SYSCTL_SYSPLL_CLK2X_DISABLE,
	.enableCLK1             = DL_SYSCTL_SYSPLL_CLK1_DISABLE,
	.enableCLK0             = DL_SYSCTL_SYSPLL_CLK0_ENABLE,
	.sysPLLMCLK             = DL_SYSCTL_SYSPLL_MCLK_CLK0,
	.sysPLLRef              = DL_SYSCTL_SYSPLL_REF_HFCLK,
	.qDiv                   = 3,
	.pDiv                   = DL_SYSCTL_SYSPLL_PDIV_1
};
SYSCONFIG_WEAK void SYSCFG_DL_SYSCTL_init(void)
{

	//Low Power Mode is configured to be SLEEP0
    DL_SYSCTL_setBORThreshold(DL_SYSCTL_BOR_THRESHOLD_LEVEL_0);
    DL_SYSCTL_setFlashWaitState(DL_SYSCTL_FLASH_WAIT_STATE_2);

    
	DL_SYSCTL_setSYSOSCFreq(DL_SYSCTL_SYSOSC_FREQ_BASE);
	/* Set default configuration */
	DL_SYSCTL_disableHFXT();
	DL_SYSCTL_disableSYSPLL();
    DL_SYSCTL_setHFCLKSourceHFXTParams(DL_SYSCTL_HFXT_RANGE_32_48_MHZ,10, true);
    DL_SYSCTL_configSYSPLL((DL_SYSCTL_SYSPLLConfig *) &gSYSPLLConfig);
    DL_SYSCTL_setULPCLKDivider(DL_SYSCTL_ULPCLK_DIV_2);
    DL_SYSCTL_enableMFCLK();
    DL_SYSCTL_setMCLKSource(SYSOSC, HSCLK, DL_SYSCTL_HSCLK_SOURCE_SYSPLL);
    /* INT_GROUP1 Priority */
    NVIC_SetPriority(GPIOA_INT_IRQn, 0);

}


/*
 * Timer clock configuration to be sourced by  / 1 (40000000 Hz)
 * timerClkFreq = (timerClkSrc / (timerClkDivRatio * (timerClkPrescale + 1)))
 *   40000000 Hz = 40000000 Hz / (1 * (0 + 1))
 */
static const DL_TimerG_ClockConfig gPWM_MOTORLClockConfig = {
    .clockSel = DL_TIMER_CLOCK_BUSCLK,
    .divideRatio = DL_TIMER_CLOCK_DIVIDE_1,
    .prescale = 0U
};

static const DL_TimerG_PWMConfig gPWM_MOTORLConfig = {
    .pwmMode = DL_TIMER_PWM_MODE_EDGE_ALIGN,
    .period = 5000,
    .isTimerWithFourCC = false,
    .startTimer = DL_TIMER_START,
};

SYSCONFIG_WEAK void SYSCFG_DL_PWM_MOTORL_init(void) {

    DL_TimerG_setClockConfig(
        PWM_MOTORL_INST, (DL_TimerG_ClockConfig *) &gPWM_MOTORLClockConfig);

    DL_TimerG_initPWMMode(
        PWM_MOTORL_INST, (DL_TimerG_PWMConfig *) &gPWM_MOTORLConfig);

    // Set Counter control to the smallest CC index being used
    DL_TimerG_setCounterControl(PWM_MOTORL_INST,DL_TIMER_CZC_CCCTL0_ZCOND,DL_TIMER_CAC_CCCTL0_ACOND,DL_TIMER_CLC_CCCTL0_LCOND);

    DL_TimerG_setCaptureCompareOutCtl(PWM_MOTORL_INST, DL_TIMER_CC_OCTL_INIT_VAL_LOW,
		DL_TIMER_CC_OCTL_INV_OUT_DISABLED, DL_TIMER_CC_OCTL_SRC_FUNCVAL,
		DL_TIMERG_CAPTURE_COMPARE_0_INDEX);

    DL_TimerG_setCaptCompUpdateMethod(PWM_MOTORL_INST, DL_TIMER_CC_UPDATE_METHOD_IMMEDIATE, DL_TIMERG_CAPTURE_COMPARE_0_INDEX);
    DL_TimerG_setCaptureCompareValue(PWM_MOTORL_INST, 5000, DL_TIMER_CC_0_INDEX);

    DL_TimerG_setCaptureCompareOutCtl(PWM_MOTORL_INST, DL_TIMER_CC_OCTL_INIT_VAL_LOW,
		DL_TIMER_CC_OCTL_INV_OUT_DISABLED, DL_TIMER_CC_OCTL_SRC_FUNCVAL,
		DL_TIMERG_CAPTURE_COMPARE_1_INDEX);

    DL_TimerG_setCaptCompUpdateMethod(PWM_MOTORL_INST, DL_TIMER_CC_UPDATE_METHOD_IMMEDIATE, DL_TIMERG_CAPTURE_COMPARE_1_INDEX);
    DL_TimerG_setCaptureCompareValue(PWM_MOTORL_INST, 5000, DL_TIMER_CC_1_INDEX);

    DL_TimerG_enableClock(PWM_MOTORL_INST);


    
    DL_TimerG_setCCPDirection(PWM_MOTORL_INST , DL_TIMER_CC0_OUTPUT | DL_TIMER_CC1_OUTPUT );


}
/*
 * Timer clock configuration to be sourced by  / 2 (40000000 Hz)
 * timerClkFreq = (timerClkSrc / (timerClkDivRatio * (timerClkPrescale + 1)))
 *   40000000 Hz = 40000000 Hz / (2 * (0 + 1))
 */
static const DL_TimerA_ClockConfig gPWM_MOTORRClockConfig = {
    .clockSel = DL_TIMER_CLOCK_BUSCLK,
    .divideRatio = DL_TIMER_CLOCK_DIVIDE_2,
    .prescale = 0U
};

static const DL_TimerA_PWMConfig gPWM_MOTORRConfig = {
    .pwmMode = DL_TIMER_PWM_MODE_EDGE_ALIGN,
    .period = 5000,
    .isTimerWithFourCC = false,
    .startTimer = DL_TIMER_START,
};

SYSCONFIG_WEAK void SYSCFG_DL_PWM_MOTORR_init(void) {

    DL_TimerA_setClockConfig(
        PWM_MOTORR_INST, (DL_TimerA_ClockConfig *) &gPWM_MOTORRClockConfig);

    DL_TimerA_initPWMMode(
        PWM_MOTORR_INST, (DL_TimerA_PWMConfig *) &gPWM_MOTORRConfig);

    // Set Counter control to the smallest CC index being used
    DL_TimerA_setCounterControl(PWM_MOTORR_INST,DL_TIMER_CZC_CCCTL0_ZCOND,DL_TIMER_CAC_CCCTL0_ACOND,DL_TIMER_CLC_CCCTL0_LCOND);

    DL_TimerA_setCaptureCompareOutCtl(PWM_MOTORR_INST, DL_TIMER_CC_OCTL_INIT_VAL_LOW,
		DL_TIMER_CC_OCTL_INV_OUT_DISABLED, DL_TIMER_CC_OCTL_SRC_FUNCVAL,
		DL_TIMERA_CAPTURE_COMPARE_0_INDEX);

    DL_TimerA_setCaptCompUpdateMethod(PWM_MOTORR_INST, DL_TIMER_CC_UPDATE_METHOD_IMMEDIATE, DL_TIMERA_CAPTURE_COMPARE_0_INDEX);
    DL_TimerA_setCaptureCompareValue(PWM_MOTORR_INST, 5000, DL_TIMER_CC_0_INDEX);

    DL_TimerA_setCaptureCompareOutCtl(PWM_MOTORR_INST, DL_TIMER_CC_OCTL_INIT_VAL_LOW,
		DL_TIMER_CC_OCTL_INV_OUT_DISABLED, DL_TIMER_CC_OCTL_SRC_FUNCVAL,
		DL_TIMERA_CAPTURE_COMPARE_1_INDEX);

    DL_TimerA_setCaptCompUpdateMethod(PWM_MOTORR_INST, DL_TIMER_CC_UPDATE_METHOD_IMMEDIATE, DL_TIMERA_CAPTURE_COMPARE_1_INDEX);
    DL_TimerA_setCaptureCompareValue(PWM_MOTORR_INST, 5000, DL_TIMER_CC_1_INDEX);

    DL_TimerA_enableClock(PWM_MOTORR_INST);


    
    DL_TimerA_setCCPDirection(PWM_MOTORR_INST , DL_TIMER_CC0_OUTPUT | DL_TIMER_CC1_OUTPUT );


}
/*
 * Timer clock configuration to be sourced by  / 8 (10000000 Hz)
 * timerClkFreq = (timerClkSrc / (timerClkDivRatio * (timerClkPrescale + 1)))
 *   10000000 Hz = 10000000 Hz / (8 * (0 + 1))
 */
static const DL_TimerG_ClockConfig gPWM_BEEPClockConfig = {
    .clockSel = DL_TIMER_CLOCK_BUSCLK,
    .divideRatio = DL_TIMER_CLOCK_DIVIDE_8,
    .prescale = 0U
};

static const DL_TimerG_PWMConfig gPWM_BEEPConfig = {
    .pwmMode = DL_TIMER_PWM_MODE_EDGE_ALIGN,
    .period = 2500,
    .isTimerWithFourCC = false,
    .startTimer = DL_TIMER_START,
};

SYSCONFIG_WEAK void SYSCFG_DL_PWM_BEEP_init(void) {

    DL_TimerG_setClockConfig(
        PWM_BEEP_INST, (DL_TimerG_ClockConfig *) &gPWM_BEEPClockConfig);

    DL_TimerG_initPWMMode(
        PWM_BEEP_INST, (DL_TimerG_PWMConfig *) &gPWM_BEEPConfig);

    // Set Counter control to the smallest CC index being used
    DL_TimerG_setCounterControl(PWM_BEEP_INST,DL_TIMER_CZC_CCCTL0_ZCOND,DL_TIMER_CAC_CCCTL0_ACOND,DL_TIMER_CLC_CCCTL0_LCOND);

    DL_TimerG_setCaptureCompareOutCtl(PWM_BEEP_INST, DL_TIMER_CC_OCTL_INIT_VAL_LOW,
		DL_TIMER_CC_OCTL_INV_OUT_DISABLED, DL_TIMER_CC_OCTL_SRC_FUNCVAL,
		DL_TIMERG_CAPTURE_COMPARE_0_INDEX);

    DL_TimerG_setCaptCompUpdateMethod(PWM_BEEP_INST, DL_TIMER_CC_UPDATE_METHOD_IMMEDIATE, DL_TIMERG_CAPTURE_COMPARE_0_INDEX);
    DL_TimerG_setCaptureCompareValue(PWM_BEEP_INST, 2500, DL_TIMER_CC_0_INDEX);

    DL_TimerG_enableClock(PWM_BEEP_INST);


    
    DL_TimerG_setCCPDirection(PWM_BEEP_INST , DL_TIMER_CC0_OUTPUT );


}
/*
 * Timer clock configuration to be sourced by  / 1 (80000000 Hz)
 * timerClkFreq = (timerClkSrc / (timerClkDivRatio * (timerClkPrescale + 1)))
 *   80000000 Hz = 80000000 Hz / (1 * (0 + 1))
 */
static const DL_TimerG_ClockConfig gSPARE_PWMClockConfig = {
    .clockSel = DL_TIMER_CLOCK_BUSCLK,
    .divideRatio = DL_TIMER_CLOCK_DIVIDE_1,
    .prescale = 0U
};

static const DL_TimerG_PWMConfig gSPARE_PWMConfig = {
    .pwmMode = DL_TIMER_PWM_MODE_EDGE_ALIGN,
    .period = 1000,
    .isTimerWithFourCC = true,
    .startTimer = DL_TIMER_STOP,
};

SYSCONFIG_WEAK void SYSCFG_DL_SPARE_PWM_init(void) {

    DL_TimerG_setClockConfig(
        SPARE_PWM_INST, (DL_TimerG_ClockConfig *) &gSPARE_PWMClockConfig);

    DL_TimerG_initPWMMode(
        SPARE_PWM_INST, (DL_TimerG_PWMConfig *) &gSPARE_PWMConfig);

    // Set Counter control to the smallest CC index being used
    DL_TimerG_setCounterControl(SPARE_PWM_INST,DL_TIMER_CZC_CCCTL0_ZCOND,DL_TIMER_CAC_CCCTL0_ACOND,DL_TIMER_CLC_CCCTL0_LCOND);

    DL_TimerG_setCaptureCompareOutCtl(SPARE_PWM_INST, DL_TIMER_CC_OCTL_INIT_VAL_LOW,
		DL_TIMER_CC_OCTL_INV_OUT_DISABLED, DL_TIMER_CC_OCTL_SRC_FUNCVAL,
		DL_TIMERG_CAPTURE_COMPARE_0_INDEX);

    DL_TimerG_setCaptCompUpdateMethod(SPARE_PWM_INST, DL_TIMER_CC_UPDATE_METHOD_IMMEDIATE, DL_TIMERG_CAPTURE_COMPARE_0_INDEX);
    DL_TimerG_setCaptureCompareValue(SPARE_PWM_INST, 1000, DL_TIMER_CC_0_INDEX);

    DL_TimerG_setCaptureCompareOutCtl(SPARE_PWM_INST, DL_TIMER_CC_OCTL_INIT_VAL_LOW,
		DL_TIMER_CC_OCTL_INV_OUT_DISABLED, DL_TIMER_CC_OCTL_SRC_FUNCVAL,
		DL_TIMERG_CAPTURE_COMPARE_1_INDEX);

    DL_TimerG_setCaptCompUpdateMethod(SPARE_PWM_INST, DL_TIMER_CC_UPDATE_METHOD_IMMEDIATE, DL_TIMERG_CAPTURE_COMPARE_1_INDEX);
    DL_TimerG_setCaptureCompareValue(SPARE_PWM_INST, 1000, DL_TIMER_CC_1_INDEX);

    DL_TimerG_enableClock(SPARE_PWM_INST);


    
    DL_TimerG_setCCPDirection(SPARE_PWM_INST , DL_TIMER_CC0_OUTPUT | DL_TIMER_CC1_OUTPUT );


}



/*
 * Timer clock configuration to be sourced by BUSCLK /  (10000000 Hz)
 * timerClkFreq = (timerClkSrc / (timerClkDivRatio * (timerClkPrescale + 1)))
 *   1000000 Hz = 10000000 Hz / (8 * (9 + 1))
 */
static const DL_TimerA_ClockConfig gTIMER_TICKClockConfig = {
    .clockSel    = DL_TIMER_CLOCK_BUSCLK,
    .divideRatio = DL_TIMER_CLOCK_DIVIDE_8,
    .prescale    = 9U,
};

/*
 * Timer load value (where the counter starts from) is calculated as (timerPeriod * timerClockFreq) - 1
 * TIMER_TICK_INST_LOAD_VALUE = (20ms * 1000000 Hz) - 1
 */
static const DL_TimerA_TimerConfig gTIMER_TICKTimerConfig = {
    .period     = TIMER_TICK_INST_LOAD_VALUE,
    .timerMode  = DL_TIMER_TIMER_MODE_PERIODIC,
    .startTimer = DL_TIMER_START,
};

SYSCONFIG_WEAK void SYSCFG_DL_TIMER_TICK_init(void) {

    DL_TimerA_setClockConfig(TIMER_TICK_INST,
        (DL_TimerA_ClockConfig *) &gTIMER_TICKClockConfig);

    DL_TimerA_initTimerMode(TIMER_TICK_INST,
        (DL_TimerA_TimerConfig *) &gTIMER_TICKTimerConfig);
    DL_TimerA_enableInterrupt(TIMER_TICK_INST , DL_TIMERA_INTERRUPT_ZERO_EVENT);
	NVIC_SetPriority(TIMER_TICK_INST_INT_IRQN, 0);
    DL_TimerA_enableClock(TIMER_TICK_INST);





}


static const DL_I2C_ClockConfig gSERVO_IICClockConfig = {
    .clockSel = DL_I2C_CLOCK_BUSCLK,
    .divideRatio = DL_I2C_CLOCK_DIVIDE_1,
};

SYSCONFIG_WEAK void SYSCFG_DL_SERVO_IIC_init(void) {

    DL_I2C_setClockConfig(SERVO_IIC_INST,
        (DL_I2C_ClockConfig *) &gSERVO_IICClockConfig);
    DL_I2C_setAnalogGlitchFilterPulseWidth(SERVO_IIC_INST,
        DL_I2C_ANALOG_GLITCH_FILTER_WIDTH_50NS);
    DL_I2C_enableAnalogGlitchFilter(SERVO_IIC_INST);




}

static const DL_UART_Main_ClockConfig gOLED_UARTClockConfig = {
    .clockSel    = DL_UART_MAIN_CLOCK_BUSCLK,
    .divideRatio = DL_UART_MAIN_CLOCK_DIVIDE_RATIO_1
};

static const DL_UART_Main_Config gOLED_UARTConfig = {
    .mode        = DL_UART_MAIN_MODE_NORMAL,
    .direction   = DL_UART_MAIN_DIRECTION_TX_RX,
    .flowControl = DL_UART_MAIN_FLOW_CONTROL_NONE,
    .parity      = DL_UART_MAIN_PARITY_NONE,
    .wordLength  = DL_UART_MAIN_WORD_LENGTH_8_BITS,
    .stopBits    = DL_UART_MAIN_STOP_BITS_ONE
};

SYSCONFIG_WEAK void SYSCFG_DL_OLED_UART_init(void)
{
    DL_UART_Main_setClockConfig(OLED_UART_INST, (DL_UART_Main_ClockConfig *) &gOLED_UARTClockConfig);

    DL_UART_Main_init(OLED_UART_INST, (DL_UART_Main_Config *) &gOLED_UARTConfig);
    /*
     * Configure baud rate by setting oversampling and baud rate divisors.
     *  Target baud rate: 9600
     *  Actual baud rate: 9599.81
     */
    DL_UART_Main_setOversampling(OLED_UART_INST, DL_UART_OVERSAMPLING_RATE_16X);
    DL_UART_Main_setBaudRateDivisor(OLED_UART_INST, OLED_UART_IBRD_40_MHZ_9600_BAUD, OLED_UART_FBRD_40_MHZ_9600_BAUD);


    /* Configure Interrupts */
    DL_UART_Main_enableInterrupt(OLED_UART_INST,
                                 DL_UART_MAIN_INTERRUPT_RX);


    DL_UART_Main_enable(OLED_UART_INST);
}
static const DL_UART_Main_ClockConfig gUART_0ClockConfig = {
    .clockSel    = DL_UART_MAIN_CLOCK_MFCLK,
    .divideRatio = DL_UART_MAIN_CLOCK_DIVIDE_RATIO_1
};

static const DL_UART_Main_Config gUART_0Config = {
    .mode        = DL_UART_MAIN_MODE_NORMAL,
    .direction   = DL_UART_MAIN_DIRECTION_TX_RX,
    .flowControl = DL_UART_MAIN_FLOW_CONTROL_NONE,
    .parity      = DL_UART_MAIN_PARITY_NONE,
    .wordLength  = DL_UART_MAIN_WORD_LENGTH_8_BITS,
    .stopBits    = DL_UART_MAIN_STOP_BITS_ONE
};

SYSCONFIG_WEAK void SYSCFG_DL_UART_0_init(void)
{
    DL_UART_Main_setClockConfig(UART_0_INST, (DL_UART_Main_ClockConfig *) &gUART_0ClockConfig);

    DL_UART_Main_init(UART_0_INST, (DL_UART_Main_Config *) &gUART_0Config);
    /*
     * Configure baud rate by setting oversampling and baud rate divisors.
     *  Target baud rate: 9600
     *  Actual baud rate: 9598.08
     */
    DL_UART_Main_setOversampling(UART_0_INST, DL_UART_OVERSAMPLING_RATE_16X);
    DL_UART_Main_setBaudRateDivisor(UART_0_INST, UART_0_IBRD_4_MHZ_9600_BAUD, UART_0_FBRD_4_MHZ_9600_BAUD);


    /* Configure Interrupts */
    DL_UART_Main_enableInterrupt(UART_0_INST,
                                 DL_UART_MAIN_INTERRUPT_RX);


    DL_UART_Main_enable(UART_0_INST);
}
static const DL_UART_Main_ClockConfig gUART_BNO080ClockConfig = {
    .clockSel    = DL_UART_MAIN_CLOCK_MFCLK,
    .divideRatio = DL_UART_MAIN_CLOCK_DIVIDE_RATIO_1
};

static const DL_UART_Main_Config gUART_BNO080Config = {
    .mode        = DL_UART_MAIN_MODE_NORMAL,
    .direction   = DL_UART_MAIN_DIRECTION_TX_RX,
    .flowControl = DL_UART_MAIN_FLOW_CONTROL_NONE,
    .parity      = DL_UART_MAIN_PARITY_NONE,
    .wordLength  = DL_UART_MAIN_WORD_LENGTH_8_BITS,
    .stopBits    = DL_UART_MAIN_STOP_BITS_ONE
};

SYSCONFIG_WEAK void SYSCFG_DL_UART_BNO080_init(void)
{
    DL_UART_Main_setClockConfig(UART_BNO080_INST, (DL_UART_Main_ClockConfig *) &gUART_BNO080ClockConfig);

    DL_UART_Main_init(UART_BNO080_INST, (DL_UART_Main_Config *) &gUART_BNO080Config);
    /*
     * Configure baud rate by setting oversampling and baud rate divisors.
     *  Target baud rate: 9600
     *  Actual baud rate: 9598.08
     */
    DL_UART_Main_setOversampling(UART_BNO080_INST, DL_UART_OVERSAMPLING_RATE_16X);
    DL_UART_Main_setBaudRateDivisor(UART_BNO080_INST, UART_BNO080_IBRD_4_MHZ_9600_BAUD, UART_BNO080_FBRD_4_MHZ_9600_BAUD);


    /* Configure Interrupts */
    DL_UART_Main_enableInterrupt(UART_BNO080_INST,
                                 DL_UART_MAIN_INTERRUPT_RX);


    DL_UART_Main_enable(UART_BNO080_INST);
}

/* POWER_ADC Initialization */
static const DL_ADC12_ClockConfig gPOWER_ADCClockConfig = {
    .clockSel       = DL_ADC12_CLOCK_SYSOSC,
    .divideRatio    = DL_ADC12_CLOCK_DIVIDE_1,
    .freqRange      = DL_ADC12_CLOCK_FREQ_RANGE_24_TO_32,
};
SYSCONFIG_WEAK void SYSCFG_DL_POWER_ADC_init(void)
{
    DL_ADC12_setClockConfig(POWER_ADC_INST, (DL_ADC12_ClockConfig *) &gPOWER_ADCClockConfig);
    DL_ADC12_configConversionMem(POWER_ADC_INST, POWER_ADC_ADCMEM_0,
        DL_ADC12_INPUT_CHAN_0, DL_ADC12_REFERENCE_VOLTAGE_VDDA, DL_ADC12_SAMPLE_TIMER_SOURCE_SCOMP0, DL_ADC12_AVERAGING_MODE_DISABLED,
        DL_ADC12_BURN_OUT_SOURCE_DISABLED, DL_ADC12_TRIGGER_MODE_AUTO_NEXT, DL_ADC12_WINDOWS_COMP_MODE_DISABLED);
    DL_ADC12_enableConversions(POWER_ADC_INST);
}

SYSCONFIG_WEAK void SYSCFG_DL_SYSTICK_init(void)
{
    /*
     * Initializes the SysTick period to 200.00 ms,
     * enables the interrupt, and starts the SysTick Timer
     */
    DL_SYSTICK_config(16000000);
}

