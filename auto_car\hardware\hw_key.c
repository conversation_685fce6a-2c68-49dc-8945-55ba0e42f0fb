#include "hw_key.h"

KEY_STATUS key_scan(void)
{
    KEY_STATUS states;
    // 读取每个按键的状态
    states.up = DL_GPIO_readPins(KEY_KEY_UP_PORT, KEY_KEY_UP_PIN) ? 1 : 0;
    states.left = DL_GPIO_readPins(KEY_KEY_LEFT_PORT, KEY_KEY_LEFT_PIN) ? 1 : 0;
    // states.right = DL_GPIO_readPins(KEY_KEY_RIGHT_PORT, KEY_KEY_RIGHT_PIN) ? 1 : 0;
    states.down = DL_GPIO_readPins(KEY_KEY_DOWN_PORT, KEY_KEY_DOWN_PIN) ? 1 : 0;
    states.mid = DL_GPIO_readPins(KEY_KEY_CENTER_PORT, KEY_KEY_CENTER_PIN) ? 1 : 0;

    return states;
}