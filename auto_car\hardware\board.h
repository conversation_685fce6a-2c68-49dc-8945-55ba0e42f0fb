//芯片头文件
#ifndef	__BOARD_H__
#define __BOARD_H__
#endif

#include "ti_msp_dl_config.h" //TI库
#include <stdio.h>//printf函数库

//硬件层
#include "hw_jy901s.h"//陀螺仪
// #include "hw_lcd.h"//LCD
// #include "hw_ble.h"//蓝牙模块
#include "hw_motor.h"//电机
#include "hw_encoder.h"//编码器
// #include "hw_tracing.h"//循迹模块
#include "hw_oled.h"//OLED
#include "hw_remind.h"//声光模块
#include "hw_key.h"//按键模块

#include "gray_sensor.h"//循迹模块



//中间层
#include "mid_timer.h"//定时器
#include "mid_button.h"//按键库（消抖）
#include "mid_remind.h"//滴答定时器触发的声光任务
#include "mid_pid.h"//位置式pid计算
#include "mid_incremental_pid.h"//增量式pid计算

//应用层
#include "app_key_task.h"//按键任务
#include "app_question_task.h"//任务状态机
#include "app_position_pid.h"//循迹pid
#include "app_angle_control.h"//角度pid
#include "app_speed_pid.h"//定速pid
#include "app_distance_pid.h"//定距pid
#include "app_gyro_pid.h"//定点转pid
#include "app_sys_mode.h"//pid调节状态机


extern uint8_t sensor_data;
extern int odometry_sum;
extern char buff[60];
extern uint8_t sensor[8];

// 在 hw_jy901s.h 中声明（可选，若其他文件需访问）
extern uint8_t g_uart3_rx_buf[BNO080_RX_BUFFER_SIZE];
extern uint8_t g_rx_index;
