<?xml version="1.0" encoding="UTF-8" ?>
<?ccsproject version="1.0"?>
<projectOptions>
	<ccsVariant value="50:Theia-based"/>
	<ccsVersion value="70.1.1"/>
	<deviceFamily value="TMS470"/>
	<connection value="common/targetdb/connections/TIXDS110_Connection.xml"/>
	<executableActions value=""/>
	<createSlaveProjects value=""/>
	<ignoreDefaultDeviceSettings value="true"/>
	<ignoreDefaultCCSSettings value="true"/>
	<templateProperties value="id=empty_LP_MSPM0G3507_nortos_ticlang.projectspec.auto_car,buildProfile=release,isHybrid=true"/>
	<activeTargetConfiguration value="targetConfigs/MSPM0G3507.ccxml"/>
	<isTargetConfigurationManual value="false"/>
	<sourceLookupPath value="${COM_TI_MSPM0_SDK_INSTALL_DIR}/source/ti/driverlib"/>
	<origin value="H:/TI-G3507/auto_car_copy"/>
	<filesToOpen value="README.md,empty.syscfg"/>
</projectOptions>
