#include "delay.h"
#include "ti_msp_dl_config.h"


#include "mid_remind.h"
#include "hw_remind.h"


volatile unsigned int delay_times = 0;

//搭配滴答定时器实现的精确ms延时
void delay_ms(unsigned int ms)
{
    delay_times = ms;
    while( delay_times != 0 );
}

void delay_s(unsigned int s)
{
    delay_times = (100*s);
    while( delay_times != 0 );
}


//滴答定时器中断服务函数 *********/
void SysTick_Handler(void)
{
    remind_update();

    if( delay_times != 0 )
    {
        delay_times--;
    }
}
