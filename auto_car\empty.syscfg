/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 */
const ADC12   = scripting.addModule("/ti/driverlib/ADC12", {}, false);
const ADC121  = ADC12.addInstance();
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const GPIO4   = GPIO.addInstance();
const GPIO5   = GPIO.addInstance();
const GPIO6   = GPIO.addInstance();
const GPIO7   = GPIO.addInstance();
const GPIO8   = GPIO.addInstance();
const GPIO9   = GPIO.addInstance();
const I2C     = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1    = I2C.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const PWM2    = PWM.addInstance();
const PWM3    = PWM.addInstance();
const PWM4    = PWM.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const TIMER   = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1  = TIMER.addInstance();
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();
const UART2   = UART.addInstance();
const UART3   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const gate7  = system.clockTree["MFCLKGATE"];
gate7.enable = true;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 4;

const mux4       = system.clockTree["EXHFMUX"];
mux4.inputSelect = "EXHFMUX_XTAL";

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL0";

const mux12       = system.clockTree["SYSPLLMUX"];
mux12.inputSelect = "zSYSPLLMUX_HFCLK";

const pinFunction4        = system.clockTree["HFXT"];
pinFunction4.inputFreq    = 40;
pinFunction4.enable       = true;
pinFunction4.HFXTStartup  = 10;
pinFunction4.HFCLKMonitor = true;

ADC121.$name                             = "POWER_ADC";
ADC121.adcPin0Config.hideOutputInversion = scripting.forceWrite(false);
ADC121.adcPin0Config.$name               = "ti_driverlib_gpio_GPIOPinGeneric13";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO1.$name                               = "GPIO_ENCODER";
GPIO1.associatedPins.create(4);
GPIO1.associatedPins[0].$name             = "PIN_LA";
GPIO1.associatedPins[0].direction         = "INPUT";
GPIO1.associatedPins[0].assignedPort      = "PORTB";
GPIO1.associatedPins[0].assignedPin       = "23";
GPIO1.associatedPins[0].interruptEn       = true;
GPIO1.associatedPins[0].interruptPriority = "0";
GPIO1.associatedPins[0].polarity          = "RISE";
GPIO1.associatedPins[1].$name             = "PIN_LB";
GPIO1.associatedPins[1].direction         = "INPUT";
GPIO1.associatedPins[1].assignedPort      = "PORTB";
GPIO1.associatedPins[1].assignedPin       = "27";
GPIO1.associatedPins[1].interruptPriority = "0";
GPIO1.associatedPins[1].polarity          = "RISE";
GPIO1.associatedPins[2].$name             = "PIN_RA";
GPIO1.associatedPins[2].direction         = "INPUT";
GPIO1.associatedPins[2].assignedPort      = "PORTA";
GPIO1.associatedPins[2].assignedPin       = "12";
GPIO1.associatedPins[2].interruptEn       = true;
GPIO1.associatedPins[2].polarity          = "RISE";
GPIO1.associatedPins[2].interruptPriority = "0";
GPIO1.associatedPins[3].$name             = "PIN_RB";
GPIO1.associatedPins[3].direction         = "INPUT";
GPIO1.associatedPins[3].assignedPort      = "PORTB";
GPIO1.associatedPins[3].assignedPin       = "6";
GPIO1.associatedPins[3].interruptPriority = "0";
GPIO1.associatedPins[3].polarity          = "RISE";

GPIO2.$name                          = "BLE";
GPIO2.associatedPins.create(2);
GPIO2.associatedPins[0].$name        = "BLE_STATE";
GPIO2.associatedPins[0].direction    = "INPUT";
GPIO2.associatedPins[0].assignedPort = "PORTB";
GPIO2.associatedPins[0].assignedPin  = "4";
GPIO2.associatedPins[1].$name        = "BLE_EN";
GPIO2.associatedPins[1].initialValue = "SET";
GPIO2.associatedPins[1].assignedPort = "PORTB";
GPIO2.associatedPins[1].assignedPin  = "5";

GPIO3.$name                              = "KEY";
GPIO3.associatedPins.create(4);
GPIO3.associatedPins[0].$name            = "KEY_CENTER";
GPIO3.associatedPins[0].direction        = "INPUT";
GPIO3.associatedPins[0].assignedPort     = "PORTB";
GPIO3.associatedPins[0].assignedPin      = "25";
GPIO3.associatedPins[0].internalResistor = "PULL_UP";
GPIO3.associatedPins[1].$name            = "KEY_DOWN";
GPIO3.associatedPins[1].direction        = "INPUT";
GPIO3.associatedPins[1].assignedPort     = "PORTB";
GPIO3.associatedPins[1].assignedPin      = "24";
GPIO3.associatedPins[1].internalResistor = "PULL_UP";
GPIO3.associatedPins[2].$name            = "KEY_LEFT";
GPIO3.associatedPins[2].direction        = "INPUT";
GPIO3.associatedPins[2].assignedPort     = "PORTA";
GPIO3.associatedPins[2].internalResistor = "PULL_UP";
GPIO3.associatedPins[2].assignedPin      = "14";
GPIO3.associatedPins[2].pin.$assign      = "PA14";
GPIO3.associatedPins[3].$name            = "KEY_UP";
GPIO3.associatedPins[3].direction        = "INPUT";
GPIO3.associatedPins[3].assignedPort     = "PORTA";
GPIO3.associatedPins[3].internalResistor = "PULL_UP";
GPIO3.associatedPins[3].assignedPin      = "13";
GPIO3.associatedPins[3].pin.$assign      = "PA13";

GPIO4.$name                          = "RANGING_IIC";
GPIO4.associatedPins.create(2);
GPIO4.associatedPins[0].$name        = "RANGING_SDA";
GPIO4.associatedPins[0].initialValue = "SET";
GPIO4.associatedPins[0].assignedPort = "PORTA";
GPIO4.associatedPins[0].assignedPin  = "26";
GPIO4.associatedPins[1].$name        = "RANGING_SCL";
GPIO4.associatedPins[1].initialValue = "SET";
GPIO4.associatedPins[1].assignedPort = "PORTA";
GPIO4.associatedPins[1].assignedPin  = "2";

GPIO5.$name                          = "LED";
GPIO5.associatedPins[0].$name        = "LED_PIN";
GPIO5.associatedPins[0].assignedPort = "PORTB";
GPIO5.associatedPins[0].assignedPin  = "22";

GPIO6.$name                          = "EXTEND_IIC";
GPIO6.associatedPins.create(2);
GPIO6.associatedPins[0].$name        = "EXTEND_SCL";
GPIO6.associatedPins[0].initialValue = "SET";
GPIO6.associatedPins[0].assignedPort = "PORTA";
GPIO6.associatedPins[0].assignedPin  = "7";
GPIO6.associatedPins[1].$name        = "EXTEND_SDA";
GPIO6.associatedPins[1].initialValue = "SET";
GPIO6.associatedPins[1].assignedPort = "PORTA";
GPIO6.associatedPins[1].assignedPin  = "18";

GPIO7.$name                          = "OLED_IIC";
GPIO7.associatedPins.create(2);
GPIO7.associatedPins[0].$name        = "OLED_SDA";
GPIO7.associatedPins[0].initialValue = "SET";
GPIO7.associatedPins[0].assignedPort = "PORTB";
GPIO7.associatedPins[0].assignedPin  = "8";
GPIO7.associatedPins[1].$name        = "OLED_SCL";
GPIO7.associatedPins[1].assignedPort = "PORTB";
GPIO7.associatedPins[1].assignedPin  = "9";

GPIO8.$name                          = "GPIO_GRAY_SERIAL";
GPIO8.associatedPins.create(2);
GPIO8.associatedPins[0].$name        = "PIN_CLK";
GPIO8.associatedPins[0].assignedPort = "PORTA";
GPIO8.associatedPins[0].assignedPin  = "0";
GPIO8.associatedPins[0].pin.$assign  = "PA0";
GPIO8.associatedPins[1].$name        = "PIN_FOWARD_DAT";
GPIO8.associatedPins[1].direction    = "INPUT";
GPIO8.associatedPins[1].pin.$assign  = "PA1";

GPIO9.$name                          = "GYRO_IIC";
GPIO9.associatedPins.create(3);
GPIO9.associatedPins[0].$name        = "GYRO_SDA";
GPIO9.associatedPins[0].assignedPort = "PORTA";
GPIO9.associatedPins[0].assignedPin  = "28";
GPIO9.associatedPins[1].$name        = "GYRO_SCL";
GPIO9.associatedPins[1].assignedPort = "PORTA";
GPIO9.associatedPins[1].assignedPin  = "31";
GPIO9.associatedPins[2].$name        = "NRST";
GPIO9.associatedPins[2].assignedPort = "PORTA";
GPIO9.associatedPins[2].assignedPin  = "24";
GPIO9.associatedPins[2].initialValue = "SET";

I2C1.$name                             = "SERVO_IIC";
I2C1.peripheral.sdaPin.$assign         = "PA30";
I2C1.peripheral.sclPin.$assign         = "PA29";
I2C1.sdaPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sdaPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sdaPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sdaPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric0";
I2C1.sclPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sclPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sclPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sclPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric1";

PWM1.$name                              = "PWM_MOTORL";
PWM1.timerCount                         = 5000;
PWM1.timerStartTimer                    = true;
PWM1.peripheral.$assign                 = "TIMG8";
PWM1.peripheral.ccp0Pin.$assign         = "PB15";
PWM1.peripheral.ccp1Pin.$assign         = "PB16";
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric8";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric9";

PWM2.$name                              = "PWM_MOTORR";
PWM2.clockDivider                       = 2;
PWM2.timerCount                         = 5000;
PWM2.timerStartTimer                    = true;
PWM2.peripheral.$assign                 = "TIMA1";
PWM2.peripheral.ccp0Pin.$assign         = "PB2";
PWM2.peripheral.ccp1Pin.$assign         = "PB3";
PWM2.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC2";
PWM2.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC3";
PWM2.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric10";
PWM2.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric11";

PWM3.$name                              = "PWM_BEEP";
PWM3.clockDivider                       = 8;
PWM3.timerStartTimer                    = true;
PWM3.ccIndex                            = [0];
PWM3.timerCount                         = 2500;
PWM3.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC4";
PWM3.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM3.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM3.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM3.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM3.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric12";
PWM3.peripheral.$assign                 = "TIMG6";

PWM4.$name                              = "SPARE_PWM";
PWM4.peripheral.ccp0Pin.$assign         = "PB20";
PWM4.peripheral.ccp1Pin.$assign         = "PA25";
PWM4.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC5";
PWM4.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC6";
PWM4.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM4.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM4.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM4.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM4.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric14";
PWM4.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM4.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM4.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM4.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM4.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric15";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;
scripting.suppress("For best practices when the CPUCLK is running at 32MHz and above, clear the flash status bit using DL_FlashCTL_executeClearStatus\\(\\) before executing any flash operation\\. Otherwise there may be false positives\\.", SYSCTL);

SYSTICK.periodEnable      = true;
SYSTICK.interruptEnable   = true;
SYSTICK.systickEnable     = true;
SYSTICK.interruptPriority = "0";
SYSTICK.period            = 16000000;

TIMER1.$name             = "TIMER_TICK";
TIMER1.timerClkDiv       = 8;
TIMER1.timerClkPrescale  = 10;
TIMER1.timerMode         = "PERIODIC";
TIMER1.timerPeriod       = "20ms";
TIMER1.timerStartTimer   = true;
TIMER1.interrupts        = ["ZERO"];
TIMER1.interruptPriority = "0";

UART1.$name                            = "OLED_UART";
UART1.enabledInterrupts                = ["RX"];
UART1.peripheral.rxPin.$assign         = "PA9";
UART1.peripheral.txPin.$assign         = "PA8";
UART1.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART1.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";
UART1.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";

UART2.$name                            = "UART_0";
UART2.uartClkSrc                       = "MFCLK";
UART2.enabledInterrupts                = ["RX"];
UART2.peripheral.rxPin.$assign         = "PA11";
UART2.peripheral.txPin.$assign         = "PA10";
UART2.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART2.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART2.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART2.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART2.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric4";
UART2.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART2.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART2.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART2.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";

UART3.uartClkSrc                       = "MFCLK";
UART3.enabledInterrupts                = ["RX"];
UART3.$name                            = "UART_BNO080";
UART3.peripheral.rxPin.$assign         = "PB13";
UART3.peripheral.txPin.$assign         = "PB12";
UART3.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART3.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART3.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART3.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART3.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric6";
UART3.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART3.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART3.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART3.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric7";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
pinFunction4.peripheral.$suggestSolution           = "SYSCTL";
pinFunction4.peripheral.hfxInPin.$suggestSolution  = "PA5";
pinFunction4.peripheral.hfxOutPin.$suggestSolution = "PA6";
ADC121.peripheral.$suggestSolution                 = "ADC0";
ADC121.peripheral.adcPin0.$suggestSolution         = "PA27";
Board.peripheral.$suggestSolution                  = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution         = "PA20";
Board.peripheral.swdioPin.$suggestSolution         = "PA19";
GPIO1.associatedPins[0].pin.$suggestSolution       = "PB23";
GPIO1.associatedPins[1].pin.$suggestSolution       = "PB27";
GPIO1.associatedPins[2].pin.$suggestSolution       = "PA12";
GPIO1.associatedPins[3].pin.$suggestSolution       = "PB6";
GPIO2.associatedPins[0].pin.$suggestSolution       = "PB4";
GPIO2.associatedPins[1].pin.$suggestSolution       = "PB5";
GPIO3.associatedPins[0].pin.$suggestSolution       = "PB25";
GPIO3.associatedPins[1].pin.$suggestSolution       = "PB24";
GPIO4.associatedPins[0].pin.$suggestSolution       = "PA26";
GPIO4.associatedPins[1].pin.$suggestSolution       = "PA2";
GPIO5.associatedPins[0].pin.$suggestSolution       = "PB22";
GPIO6.associatedPins[0].pin.$suggestSolution       = "PA7";
GPIO6.associatedPins[1].pin.$suggestSolution       = "PA18";
GPIO7.associatedPins[0].pin.$suggestSolution       = "PB8";
GPIO7.associatedPins[1].pin.$suggestSolution       = "PB9";
GPIO9.associatedPins[0].pin.$suggestSolution       = "PA28";
GPIO9.associatedPins[1].pin.$suggestSolution       = "PA31";
GPIO9.associatedPins[2].pin.$suggestSolution       = "PA24";
I2C1.peripheral.$suggestSolution                   = "I2C1";
PWM3.peripheral.ccp0Pin.$suggestSolution           = "PA21";
PWM4.peripheral.$suggestSolution                   = "TIMG12";
TIMER1.peripheral.$suggestSolution                 = "TIMA0";
UART1.peripheral.$suggestSolution                  = "UART1";
UART2.peripheral.$suggestSolution                  = "UART0";
UART3.peripheral.$suggestSolution                  = "UART3";
