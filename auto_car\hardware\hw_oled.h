#ifndef __OLED_H
#define __OLED_H 

#include "ti_msp_dl_config.h"

//-----------------OLED端口定义---------------- 

#define OLED_SCL_Clr() DL_GPIO_clearPins(OLED_IIC_PORT,OLED_IIC_OLED_SCL_PIN)//SCL
#define OLED_SCL_Set() DL_GPIO_setPins(OLED_IIC_PORT,OLED_IIC_OLED_SCL_PIN)

#define OLED_SDA_Clr() DL_GPIO_clearPins(OLED_IIC_PORT,OLED_IIC_OLED_SDA_PIN)//DIN
#define OLED_SDA_Set() DL_GPIO_setPins(OLED_IIC_PORT,OLED_IIC_OLED_SDA_PIN)


#define OLED_CMD  0	//写命令
#define OLED_DATA 1	//写数据

void OLED_ColorTurn(unsigned char i);//内容反显 i=0正常=1反显
void OLED_DisplayTurn(unsigned char i);//屏幕旋转180度 i=0正常=1翻转180
void OLED_DisPlay_On(void);//开启OLED背光
void OLED_DisPlay_Off(void);//关闭OLED背光
void OLED_Refresh(void);//更新OLED的显示
void OLED_Clear(void);//清屏(内部带有 OLED_Refresh() )
void OLED_DrawPoint(unsigned char x,unsigned char y,unsigned char t);//画点
void OLED_DrawLine(unsigned char x1,unsigned char y1,unsigned char x2,unsigned char y2,unsigned char mode);//画线
void OLED_DrawCircle(unsigned char x,unsigned char y,unsigned char r);//画圆
void OLED_ShowChar(unsigned char x,unsigned char y,unsigned char chr,unsigned char size1,unsigned char mode);//显示单个ASCII字符
void OLED_ShowString(unsigned char x,unsigned char y,unsigned char *chr,unsigned char size1,unsigned char mode);//显示ASCII字符串
void OLED_ShowChinese(unsigned char x,unsigned char y,unsigned char num,unsigned char size1,unsigned char mode);//显示单个汉字
void OLED_ScrollDisplay(unsigned char num,unsigned char space,unsigned char mode);//滚动显示汉字
void OLED_ShowPicture(unsigned char x,unsigned char y,unsigned char sizex,unsigned char sizey,unsigned char BMP[],unsigned char mode);//显示图片
void OLED_Init(void);//OLED的初始化
#endif

