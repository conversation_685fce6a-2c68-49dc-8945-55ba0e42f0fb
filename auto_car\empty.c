#include "board.h"

// 灰度传感器数据
uint8_t sensor_data = 0;
uint8_t sensor[8] = {0};

// 小车控制巡线所需变量
volatile int BaseSpeed = 27;
volatile int MaxSpeed = 35;
volatile int Duty_L = 0, Duty_R = 0;
volatile uint8_t stop_flag = 0;
volatile uint8_t wandao_sudu = 0; // 0：快速弯道    1：慢速弯道
volatile uint8_t jiajiansu_count = 0;
volatile uint8_t jiasu_flag = 0;

char buff[60] = {0};

// 陀螺仪传感器数据
BNO080_Data_t BNO080_Data;

int main(void) {
  // OLED字符串缓冲区
  char buff[100] = {0};

  // TI库初始化
  SYSCFG_DL_init();

  // 编码器初始化
  encoder_init();
  stop_motor();

  // 定时器初始化
  timer_init();

  // 按键任务初始化
  user_button_init();

  // 系统状态初始化
  sys_event_init();

  // OLED初始化
  OLED_Init();
  OLED_Clear();

  // 状态机初始化
  State_Machine_init();

  // 串口0初始化
  uart_init(); //{printf("sada");uart0_send_char("123");}
  uart3_init();

  // 初始化硬件（GPIO、I2C引脚）
  DL_GPIO_initDigitalOutput(GYRO_IIC_GYRO_SCL_IOMUX);
  DL_GPIO_initDigitalOutput(GYRO_IIC_GYRO_SDA_IOMUX);
  DL_GPIO_initDigitalOutput(GYRO_IIC_NRST_IOMUX); // 复位引脚

  NVIC_EnableIRQ(UART3_INT_IRQn);


  while (1) {

    system_time++;

    // 按键触发任务切换
    if (key_1_flag) {
      remind_flag = 1;
      State_Machine.Main_State = QUESTION_1;
      key_1_flag = 0;
    }
    if (key_2_flag) {
      remind_flag = 1;
      State_Machine.Main_State = QUESTION_2;
      key_2_flag = 0;
    }
    if (key_3_flag) {
      remind_flag = 1;
      State_Machine.Main_State = QUESTION_3;
      key_3_flag = 0;
    }
    if (key_4_flag) {
      remind_flag = 1;
      State_Machine.Main_State = QUESTION_4;
      key_4_flag = 0;
    }

    sensor_data = gray_serial_forward_read();
    extractSensorData(sensor_data, sensor);
    BNO080_GetData(&BNO080_Data);

    // 根据主状态执行对应任务
    switch (State_Machine.Main_State) {
    case STOP_STATE:
      stop_motor();
      break;

    case QUESTION_1:
      Question_Task_1();
      break;

    case QUESTION_2:
      Question_Task_2();
      break;

    case QUESTION_3:
      Question_Task_3();
      break;

    case QUESTION_4:
      Question_Task_4();
      break;

    default:
      break;
    }
    
    // 每隔一定时间显示当前数据：角度、里程、循迹值
    if (system_time % 50 == 0) // 1*10=50ms的任务
    {
      //// 显示角度
      sprintf(buff, "angle = %5.2f  ", BNO080_Data.yaw);
      OLED_ShowString(0, 16, (uint8_t *)buff, 12, 1);

      // 显示里程数
      sprintf(buff, "odo = %d  ", key_1_flag);
      OLED_ShowString(0, 32, (uint8_t *)buff, 12, 1);

      // 显示循迹偏差数
      sprintf(buff, "black = %d  ", NumofZero());
      OLED_ShowString(0, 48, (uint8_t *)buff, 12, 1);

      sprintf(buff, "%d-%d-%d-%d-%d-%d-%d", sensor[0], sensor[1], sensor[2],
              sensor[3], sensor[4], sensor[5], sensor[6]);
      OLED_ShowString(0, 0, (uint8_t *)buff, 12, 1);

      // 显示速度
      sprintf(buff, "lv:%d  ", get_right_encoder());
      OLED_ShowString(65, 32, (uint8_t *)buff, 12, 1);

      sprintf(buff, "rv:%d  ", get_left_encoder());
      OLED_ShowString(65, 48, (uint8_t *)buff, 12, 1);
    }

    // 每隔一定时间刷新OLED显示
    if (system_time % 100 == 0) // 1*100=100ms的任务
    {
      // 刷新屏幕内容
      OLED_Refresh();
    }
  }
}
