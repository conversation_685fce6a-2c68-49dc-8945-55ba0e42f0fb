/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3507

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)


#define GPIO_HFXT_PORT                                                     GPIOA
#define GPIO_HFXIN_PIN                                             DL_GPIO_PIN_5
#define GPIO_HFXIN_IOMUX                                         (IOMUX_PINCM10)
#define GPIO_HFXOUT_PIN                                            DL_GPIO_PIN_6
#define GPIO_HFXOUT_IOMUX                                        (IOMUX_PINCM11)
#define CPUCLK_FREQ                                                     80000000



/* Defines for PWM_MOTORL */
#define PWM_MOTORL_INST                                                    TIMG8
#define PWM_MOTORL_INST_IRQHandler                              TIMG8_IRQHandler
#define PWM_MOTORL_INST_INT_IRQN                                (TIMG8_INT_IRQn)
#define PWM_MOTORL_INST_CLK_FREQ                                        40000000
/* GPIO defines for channel 0 */
#define GPIO_PWM_MOTORL_C0_PORT                                            GPIOB
#define GPIO_PWM_MOTORL_C0_PIN                                    DL_GPIO_PIN_15
#define GPIO_PWM_MOTORL_C0_IOMUX                                 (IOMUX_PINCM32)
#define GPIO_PWM_MOTORL_C0_IOMUX_FUNC                IOMUX_PINCM32_PF_TIMG8_CCP0
#define GPIO_PWM_MOTORL_C0_IDX                               DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_PWM_MOTORL_C1_PORT                                            GPIOB
#define GPIO_PWM_MOTORL_C1_PIN                                    DL_GPIO_PIN_16
#define GPIO_PWM_MOTORL_C1_IOMUX                                 (IOMUX_PINCM33)
#define GPIO_PWM_MOTORL_C1_IOMUX_FUNC                IOMUX_PINCM33_PF_TIMG8_CCP1
#define GPIO_PWM_MOTORL_C1_IDX                               DL_TIMER_CC_1_INDEX

/* Defines for PWM_MOTORR */
#define PWM_MOTORR_INST                                                    TIMA1
#define PWM_MOTORR_INST_IRQHandler                              TIMA1_IRQHandler
#define PWM_MOTORR_INST_INT_IRQN                                (TIMA1_INT_IRQn)
#define PWM_MOTORR_INST_CLK_FREQ                                        40000000
/* GPIO defines for channel 0 */
#define GPIO_PWM_MOTORR_C0_PORT                                            GPIOB
#define GPIO_PWM_MOTORR_C0_PIN                                     DL_GPIO_PIN_2
#define GPIO_PWM_MOTORR_C0_IOMUX                                 (IOMUX_PINCM15)
#define GPIO_PWM_MOTORR_C0_IOMUX_FUNC                IOMUX_PINCM15_PF_TIMA1_CCP0
#define GPIO_PWM_MOTORR_C0_IDX                               DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_PWM_MOTORR_C1_PORT                                            GPIOB
#define GPIO_PWM_MOTORR_C1_PIN                                     DL_GPIO_PIN_3
#define GPIO_PWM_MOTORR_C1_IOMUX                                 (IOMUX_PINCM16)
#define GPIO_PWM_MOTORR_C1_IOMUX_FUNC                IOMUX_PINCM16_PF_TIMA1_CCP1
#define GPIO_PWM_MOTORR_C1_IDX                               DL_TIMER_CC_1_INDEX

/* Defines for PWM_BEEP */
#define PWM_BEEP_INST                                                      TIMG6
#define PWM_BEEP_INST_IRQHandler                                TIMG6_IRQHandler
#define PWM_BEEP_INST_INT_IRQN                                  (TIMG6_INT_IRQn)
#define PWM_BEEP_INST_CLK_FREQ                                          10000000
/* GPIO defines for channel 0 */
#define GPIO_PWM_BEEP_C0_PORT                                              GPIOA
#define GPIO_PWM_BEEP_C0_PIN                                      DL_GPIO_PIN_21
#define GPIO_PWM_BEEP_C0_IOMUX                                   (IOMUX_PINCM46)
#define GPIO_PWM_BEEP_C0_IOMUX_FUNC                  IOMUX_PINCM46_PF_TIMG6_CCP0
#define GPIO_PWM_BEEP_C0_IDX                                 DL_TIMER_CC_0_INDEX

/* Defines for SPARE_PWM */
#define SPARE_PWM_INST                                                    TIMG12
#define SPARE_PWM_INST_IRQHandler                              TIMG12_IRQHandler
#define SPARE_PWM_INST_INT_IRQN                                (TIMG12_INT_IRQn)
#define SPARE_PWM_INST_CLK_FREQ                                         80000000
/* GPIO defines for channel 0 */
#define GPIO_SPARE_PWM_C0_PORT                                             GPIOB
#define GPIO_SPARE_PWM_C0_PIN                                     DL_GPIO_PIN_20
#define GPIO_SPARE_PWM_C0_IOMUX                                  (IOMUX_PINCM48)
#define GPIO_SPARE_PWM_C0_IOMUX_FUNC                IOMUX_PINCM48_PF_TIMG12_CCP0
#define GPIO_SPARE_PWM_C0_IDX                                DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_SPARE_PWM_C1_PORT                                             GPIOA
#define GPIO_SPARE_PWM_C1_PIN                                     DL_GPIO_PIN_25
#define GPIO_SPARE_PWM_C1_IOMUX                                  (IOMUX_PINCM55)
#define GPIO_SPARE_PWM_C1_IOMUX_FUNC                IOMUX_PINCM55_PF_TIMG12_CCP1
#define GPIO_SPARE_PWM_C1_IDX                                DL_TIMER_CC_1_INDEX



/* Defines for TIMER_TICK */
#define TIMER_TICK_INST                                                  (TIMA0)
#define TIMER_TICK_INST_IRQHandler                              TIMA0_IRQHandler
#define TIMER_TICK_INST_INT_IRQN                                (TIMA0_INT_IRQn)
#define TIMER_TICK_INST_LOAD_VALUE                                      (19999U)




/* Defines for SERVO_IIC */
#define SERVO_IIC_INST                                                      I2C1
#define SERVO_IIC_INST_IRQHandler                                I2C1_IRQHandler
#define SERVO_IIC_INST_INT_IRQN                                    I2C1_INT_IRQn
#define GPIO_SERVO_IIC_SDA_PORT                                            GPIOA
#define GPIO_SERVO_IIC_SDA_PIN                                    DL_GPIO_PIN_30
#define GPIO_SERVO_IIC_IOMUX_SDA                                  (IOMUX_PINCM5)
#define GPIO_SERVO_IIC_IOMUX_SDA_FUNC                   IOMUX_PINCM5_PF_I2C1_SDA
#define GPIO_SERVO_IIC_SCL_PORT                                            GPIOA
#define GPIO_SERVO_IIC_SCL_PIN                                    DL_GPIO_PIN_29
#define GPIO_SERVO_IIC_IOMUX_SCL                                  (IOMUX_PINCM4)
#define GPIO_SERVO_IIC_IOMUX_SCL_FUNC                   IOMUX_PINCM4_PF_I2C1_SCL


/* Defines for OLED_UART */
#define OLED_UART_INST                                                     UART1
#define OLED_UART_INST_FREQUENCY                                        40000000
#define OLED_UART_INST_IRQHandler                               UART1_IRQHandler
#define OLED_UART_INST_INT_IRQN                                   UART1_INT_IRQn
#define GPIO_OLED_UART_RX_PORT                                             GPIOA
#define GPIO_OLED_UART_TX_PORT                                             GPIOA
#define GPIO_OLED_UART_RX_PIN                                      DL_GPIO_PIN_9
#define GPIO_OLED_UART_TX_PIN                                      DL_GPIO_PIN_8
#define GPIO_OLED_UART_IOMUX_RX                                  (IOMUX_PINCM20)
#define GPIO_OLED_UART_IOMUX_TX                                  (IOMUX_PINCM19)
#define GPIO_OLED_UART_IOMUX_RX_FUNC                   IOMUX_PINCM20_PF_UART1_RX
#define GPIO_OLED_UART_IOMUX_TX_FUNC                   IOMUX_PINCM19_PF_UART1_TX
#define OLED_UART_BAUD_RATE                                               (9600)
#define OLED_UART_IBRD_40_MHZ_9600_BAUD                                    (260)
#define OLED_UART_FBRD_40_MHZ_9600_BAUD                                     (27)
/* Defines for UART_0 */
#define UART_0_INST                                                        UART0
#define UART_0_INST_FREQUENCY                                            4000000
#define UART_0_INST_IRQHandler                                  UART0_IRQHandler
#define UART_0_INST_INT_IRQN                                      UART0_INT_IRQn
#define GPIO_UART_0_RX_PORT                                                GPIOA
#define GPIO_UART_0_TX_PORT                                                GPIOA
#define GPIO_UART_0_RX_PIN                                        DL_GPIO_PIN_11
#define GPIO_UART_0_TX_PIN                                        DL_GPIO_PIN_10
#define GPIO_UART_0_IOMUX_RX                                     (IOMUX_PINCM22)
#define GPIO_UART_0_IOMUX_TX                                     (IOMUX_PINCM21)
#define GPIO_UART_0_IOMUX_RX_FUNC                      IOMUX_PINCM22_PF_UART0_RX
#define GPIO_UART_0_IOMUX_TX_FUNC                      IOMUX_PINCM21_PF_UART0_TX
#define UART_0_BAUD_RATE                                                  (9600)
#define UART_0_IBRD_4_MHZ_9600_BAUD                                         (26)
#define UART_0_FBRD_4_MHZ_9600_BAUD                                          (3)
/* Defines for UART_BNO080 */
#define UART_BNO080_INST                                                   UART3
#define UART_BNO080_INST_FREQUENCY                                       4000000
#define UART_BNO080_INST_IRQHandler                             UART3_IRQHandler
#define UART_BNO080_INST_INT_IRQN                                 UART3_INT_IRQn
#define GPIO_UART_BNO080_RX_PORT                                           GPIOB
#define GPIO_UART_BNO080_TX_PORT                                           GPIOB
#define GPIO_UART_BNO080_RX_PIN                                   DL_GPIO_PIN_13
#define GPIO_UART_BNO080_TX_PIN                                   DL_GPIO_PIN_12
#define GPIO_UART_BNO080_IOMUX_RX                                (IOMUX_PINCM30)
#define GPIO_UART_BNO080_IOMUX_TX                                (IOMUX_PINCM29)
#define GPIO_UART_BNO080_IOMUX_RX_FUNC                 IOMUX_PINCM30_PF_UART3_RX
#define GPIO_UART_BNO080_IOMUX_TX_FUNC                 IOMUX_PINCM29_PF_UART3_TX
#define UART_BNO080_BAUD_RATE                                             (9600)
#define UART_BNO080_IBRD_4_MHZ_9600_BAUD                                    (26)
#define UART_BNO080_FBRD_4_MHZ_9600_BAUD                                     (3)





/* Defines for POWER_ADC */
#define POWER_ADC_INST                                                      ADC0
#define POWER_ADC_INST_IRQHandler                                ADC0_IRQHandler
#define POWER_ADC_INST_INT_IRQN                                  (ADC0_INT_IRQn)
#define POWER_ADC_ADCMEM_0                                    DL_ADC12_MEM_IDX_0
#define POWER_ADC_ADCMEM_0_REF                   DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define POWER_ADC_ADCMEM_0_REF_VOLTAGE_V                                     3.3
#define GPIO_POWER_ADC_C0_PORT                                             GPIOA
#define GPIO_POWER_ADC_C0_PIN                                     DL_GPIO_PIN_27



/* Port definition for Pin Group LED */
#define LED_PORT                                                         (GPIOB)

/* Defines for LED_PIN: GPIOB.22 with pinCMx 50 on package pin 21 */
#define LED_LED_PIN_PIN                                         (DL_GPIO_PIN_22)
#define LED_LED_PIN_IOMUX                                        (IOMUX_PINCM50)
/* Defines for PIN_LA: GPIOB.23 with pinCMx 51 on package pin 22 */
#define GPIO_ENCODER_PIN_LA_PORT                                         (GPIOB)
// pins affected by this interrupt request:["PIN_LA"]
#define GPIO_ENCODER_GPIOB_INT_IRQN                             (GPIOB_INT_IRQn)
#define GPIO_ENCODER_GPIOB_INT_IIDX             (DL_INTERRUPT_GROUP1_IIDX_GPIOB)
#define GPIO_ENCODER_PIN_LA_IIDX                            (DL_GPIO_IIDX_DIO23)
#define GPIO_ENCODER_PIN_LA_PIN                                 (DL_GPIO_PIN_23)
#define GPIO_ENCODER_PIN_LA_IOMUX                                (IOMUX_PINCM51)
/* Defines for PIN_LB: GPIOB.27 with pinCMx 58 on package pin 29 */
#define GPIO_ENCODER_PIN_LB_PORT                                         (GPIOB)
#define GPIO_ENCODER_PIN_LB_PIN                                 (DL_GPIO_PIN_27)
#define GPIO_ENCODER_PIN_LB_IOMUX                                (IOMUX_PINCM58)
/* Defines for PIN_RA: GPIOA.12 with pinCMx 34 on package pin 5 */
#define GPIO_ENCODER_PIN_RA_PORT                                         (GPIOA)
// pins affected by this interrupt request:["PIN_RA"]
#define GPIO_ENCODER_GPIOA_INT_IRQN                             (GPIOA_INT_IRQn)
#define GPIO_ENCODER_GPIOA_INT_IIDX             (DL_INTERRUPT_GROUP1_IIDX_GPIOA)
#define GPIO_ENCODER_PIN_RA_IIDX                            (DL_GPIO_IIDX_DIO12)
#define GPIO_ENCODER_PIN_RA_PIN                                 (DL_GPIO_PIN_12)
#define GPIO_ENCODER_PIN_RA_IOMUX                                (IOMUX_PINCM34)
/* Defines for PIN_RB: GPIOB.6 with pinCMx 23 on package pin 58 */
#define GPIO_ENCODER_PIN_RB_PORT                                         (GPIOB)
#define GPIO_ENCODER_PIN_RB_PIN                                  (DL_GPIO_PIN_6)
#define GPIO_ENCODER_PIN_RB_IOMUX                                (IOMUX_PINCM23)
/* Port definition for Pin Group BLE */
#define BLE_PORT                                                         (GPIOB)

/* Defines for BLE_STATE: GPIOB.4 with pinCMx 17 on package pin 52 */
#define BLE_BLE_STATE_PIN                                        (DL_GPIO_PIN_4)
#define BLE_BLE_STATE_IOMUX                                      (IOMUX_PINCM17)
/* Defines for BLE_EN: GPIOB.5 with pinCMx 18 on package pin 53 */
#define BLE_BLE_EN_PIN                                           (DL_GPIO_PIN_5)
#define BLE_BLE_EN_IOMUX                                         (IOMUX_PINCM18)
/* Defines for KEY_CENTER: GPIOB.25 with pinCMx 56 on package pin 27 */
#define KEY_KEY_CENTER_PORT                                              (GPIOB)
#define KEY_KEY_CENTER_PIN                                      (DL_GPIO_PIN_25)
#define KEY_KEY_CENTER_IOMUX                                     (IOMUX_PINCM56)
/* Defines for KEY_DOWN: GPIOB.24 with pinCMx 52 on package pin 23 */
#define KEY_KEY_DOWN_PORT                                                (GPIOB)
#define KEY_KEY_DOWN_PIN                                        (DL_GPIO_PIN_24)
#define KEY_KEY_DOWN_IOMUX                                       (IOMUX_PINCM52)
/* Defines for KEY_LEFT: GPIOA.14 with pinCMx 36 on package pin 7 */
#define KEY_KEY_LEFT_PORT                                                (GPIOA)
#define KEY_KEY_LEFT_PIN                                        (DL_GPIO_PIN_14)
#define KEY_KEY_LEFT_IOMUX                                       (IOMUX_PINCM36)
/* Defines for KEY_UP: GPIOA.13 with pinCMx 35 on package pin 6 */
#define KEY_KEY_UP_PORT                                                  (GPIOA)
#define KEY_KEY_UP_PIN                                          (DL_GPIO_PIN_13)
#define KEY_KEY_UP_IOMUX                                         (IOMUX_PINCM35)
/* Port definition for Pin Group RANGING_IIC */
#define RANGING_IIC_PORT                                                 (GPIOA)

/* Defines for RANGING_SDA: GPIOA.26 with pinCMx 59 on package pin 30 */
#define RANGING_IIC_RANGING_SDA_PIN                             (DL_GPIO_PIN_26)
#define RANGING_IIC_RANGING_SDA_IOMUX                            (IOMUX_PINCM59)
/* Defines for RANGING_SCL: GPIOA.2 with pinCMx 7 on package pin 42 */
#define RANGING_IIC_RANGING_SCL_PIN                              (DL_GPIO_PIN_2)
#define RANGING_IIC_RANGING_SCL_IOMUX                             (IOMUX_PINCM7)
/* Port definition for Pin Group EXTEND_IIC */
#define EXTEND_IIC_PORT                                                  (GPIOA)

/* Defines for EXTEND_SCL: GPIOA.7 with pinCMx 14 on package pin 49 */
#define EXTEND_IIC_EXTEND_SCL_PIN                                (DL_GPIO_PIN_7)
#define EXTEND_IIC_EXTEND_SCL_IOMUX                              (IOMUX_PINCM14)
/* Defines for EXTEND_SDA: GPIOA.18 with pinCMx 40 on package pin 11 */
#define EXTEND_IIC_EXTEND_SDA_PIN                               (DL_GPIO_PIN_18)
#define EXTEND_IIC_EXTEND_SDA_IOMUX                              (IOMUX_PINCM40)
/* Port definition for Pin Group OLED_IIC */
#define OLED_IIC_PORT                                                    (GPIOB)

/* Defines for OLED_SDA: GPIOB.8 with pinCMx 25 on package pin 60 */
#define OLED_IIC_OLED_SDA_PIN                                    (DL_GPIO_PIN_8)
#define OLED_IIC_OLED_SDA_IOMUX                                  (IOMUX_PINCM25)
/* Defines for OLED_SCL: GPIOB.9 with pinCMx 26 on package pin 61 */
#define OLED_IIC_OLED_SCL_PIN                                    (DL_GPIO_PIN_9)
#define OLED_IIC_OLED_SCL_IOMUX                                  (IOMUX_PINCM26)
/* Port definition for Pin Group GPIO_GRAY_SERIAL */
#define GPIO_GRAY_SERIAL_PORT                                            (GPIOA)

/* Defines for PIN_CLK: GPIOA.0 with pinCMx 1 on package pin 33 */
#define GPIO_GRAY_SERIAL_PIN_CLK_PIN                             (DL_GPIO_PIN_0)
#define GPIO_GRAY_SERIAL_PIN_CLK_IOMUX                            (IOMUX_PINCM1)
/* Defines for PIN_FOWARD_DAT: GPIOA.1 with pinCMx 2 on package pin 34 */
#define GPIO_GRAY_SERIAL_PIN_FOWARD_DAT_PIN                      (DL_GPIO_PIN_1)
#define GPIO_GRAY_SERIAL_PIN_FOWARD_DAT_IOMUX                     (IOMUX_PINCM2)
/* Port definition for Pin Group GYRO_IIC */
#define GYRO_IIC_PORT                                                    (GPIOA)

/* Defines for GYRO_SDA: GPIOA.28 with pinCMx 3 on package pin 35 */
#define GYRO_IIC_GYRO_SDA_PIN                                   (DL_GPIO_PIN_28)
#define GYRO_IIC_GYRO_SDA_IOMUX                                   (IOMUX_PINCM3)
/* Defines for GYRO_SCL: GPIOA.31 with pinCMx 6 on package pin 39 */
#define GYRO_IIC_GYRO_SCL_PIN                                   (DL_GPIO_PIN_31)
#define GYRO_IIC_GYRO_SCL_IOMUX                                   (IOMUX_PINCM6)
/* Defines for NRST: GPIOA.24 with pinCMx 54 on package pin 25 */
#define GYRO_IIC_NRST_PIN                                       (DL_GPIO_PIN_24)
#define GYRO_IIC_NRST_IOMUX                                      (IOMUX_PINCM54)



/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_PWM_MOTORL_init(void);
void SYSCFG_DL_PWM_MOTORR_init(void);
void SYSCFG_DL_PWM_BEEP_init(void);
void SYSCFG_DL_SPARE_PWM_init(void);
void SYSCFG_DL_TIMER_TICK_init(void);
void SYSCFG_DL_SERVO_IIC_init(void);
void SYSCFG_DL_OLED_UART_init(void);
void SYSCFG_DL_UART_0_init(void);
void SYSCFG_DL_UART_BNO080_init(void);
void SYSCFG_DL_POWER_ADC_init(void);

void SYSCFG_DL_SYSTICK_init(void);

bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
