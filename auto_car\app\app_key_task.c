#include "app_key_task.h"
#include "app_sys_mode.h"

//按键触发任务标志位
uint8_t key_1_flag = 0;
uint8_t key_2_flag = 0;
uint8_t key_3_flag = 0;
uint8_t key_4_flag = 0;




void btn_up_cb(flex_button_t *btn)
{ 
    switch (btn->event)
    {
        case FLEX_BTN_PRESS_CLICK://单击事件 
        {
            key_1_flag = 1;          
            break;
        }
        case FLEX_BTN_PRESS_LONG_HOLD://长按保持事件 
        {
            event_manager(&system_status, LONG_PRESS_ADD_START_EVENT);
            break;
        }
           

        case FLEX_BTN_PRESS_LONG_HOLD_UP://长按保持后抬起事件 
        {
            event_manager(&system_status, LONG_PRESS_END_EVENT);
            break;
        }
            
        default:break;
    }
}

void btn_left_cb(flex_button_t *btn)
{
    switch (btn->event)
    {
        case FLEX_BTN_PRESS_CLICK://单击事件
        {
            key_2_flag = 1;
            break;
        }
            
        default:break;
    }
}

void btn_right_cb(flex_button_t *btn)
{
    switch (btn->event)
    {
        case FLEX_BTN_PRESS_CLICK://单击事件
        {
            key_3_flag = 1;
            break;
        }
            
        case FLEX_BTN_PRESS_LONG_START://长击开始事件
            break;

        default:break;
    }
}

void btn_down_cb(flex_button_t *btn)
{
    switch (btn->event)
    {
        case FLEX_BTN_PRESS_CLICK://单击事件   
        {
            key_4_flag = 1;       
            break;
        }

        case FLEX_BTN_PRESS_LONG_HOLD://长按保持事件 
        {
            event_manager(&system_status, LONG_PRESS_SUBTRACT_START_EVENT);
            break;
        }
           
        case FLEX_BTN_PRESS_LONG_HOLD_UP://长按保持后抬起事件 
        {
            event_manager(&system_status, LONG_PRESS_END_EVENT);
            break;
        }
            
        default:break;
    }
}

void btn_mid_cb(flex_button_t *btn)
{
    switch (btn->event)
    {
        case FLEX_BTN_PRESS_CLICK://单击事件       
            break;

        case FLEX_BTN_PRESS_LONG_HOLD://长按保持事件 
            break;

        case FLEX_BTN_PRESS_LONG_HOLD_UP://长按保持后抬起事件 
            break;

        default:break;
    }
}
