#ifndef _APP_KEY_TASK_H_
#define _APP_KEY_TASK_H_

#include "ti_msp_dl_config.h"
#include "mid_button.h"

void btn_up_cb(flex_button_t *btn);
void btn_left_cb(flex_button_t *btn);
void btn_right_cb(flex_button_t *btn);
void btn_down_cb(flex_button_t *btn);
void btn_mid_cb(flex_button_t *btn);

extern uint8_t key_1_flag;
extern uint8_t key_2_flag;
extern uint8_t key_3_flag;
extern uint8_t key_4_flag;

#endif
