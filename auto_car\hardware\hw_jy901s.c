#include "hw_jy901s.h"
#include "stdio.h"
#include "string.h"
#include <stdint.h>
#include <string.h>

// #include "fsl_uart.h"    // 包含UART相关函数/宏定义
// #include "fsl_iomuxc.h"  // 包含IOMUXC_PinMuxSet定义
// #include "fsl_dma.h"     // 包含DMA相关定义





float target_angle = 0;                    //The target heading_Angle of the car movement(车辆运动的目标航向角)






// 在 hw_jy901s.c 中定义
uint8_t g_uart3_rx_buf[BNO080_RX_BUFFER_SIZE] = {0};
uint8_t g_rx_index = 0;


/* 全局变量 */
uint8_t g_usart3_dma_rx_buf[BNO080_RX_BUFFER_SIZE];
static BNO080_Data_t g_bno080_data = {0};

/**
 * 初始化BNO080传感器的GPIO和UART3
 */


/**
 * 获取BNO080的角度数据
 * @param data_ptr 指向BNO080_Data_t结构体的指针，用于存储数据
 */
// 获取解析后的数据
void BNO080_GetData(BNO080_Data_t* data_ptr) {
    memcpy(data_ptr, &g_bno080_data, sizeof(BNO080_Data_t));
    data_ptr->new_data_flag = 0; // 清除标志
}

// 解析RVC格式数据包
static void BNO080_ParseData(void) {
    // 检查帧头（0xAAAA）
    if (*(uint16_t*)g_uart3_rx_buf != RVC_HEADER) {
        return;
    }
    // 计算校验和
    uint8_t checksum = 0;
    for (int i = 2; i < BNO080_RX_BUFFER_SIZE - 1; i++) {
        checksum += g_uart3_rx_buf[i];
    }
    if (checksum != g_uart3_rx_buf[BNO080_RX_BUFFER_SIZE - 1]) {
        return;
    }
    // 提取角度数据（根据BNO080 RVC格式，单位0.01°）
    int16_t temp;
    memcpy(&temp, &g_uart3_rx_buf[3], 2); // Yaw
    g_bno080_data.yaw = temp * 0.01f;
    memcpy(&temp, &g_uart3_rx_buf[5], 2); // Pitch
    g_bno080_data.pitch = temp * 0.01f;
    memcpy(&temp, &g_uart3_rx_buf[7], 2); // Roll
    g_bno080_data.roll = temp * 0.01f;
    g_bno080_data.new_data_flag = 1;
}

// UART3中断处理函数（接收数据）
void UART3_INT_IRQHandler   (void) {
    if (DL_UART_getPendingInterrupt(UART_BNO080_INST) & DL_UART_INTERRUPT_RX) 
    {
        uint8_t data = DL_UART_receiveData(UART_BNO080_INST);
        // 循环填充缓冲区
        g_uart3_rx_buf[g_rx_index++] = data;
        if (g_rx_index >= BNO080_RX_BUFFER_SIZE) {
            g_rx_index = 0;
            BNO080_ParseData(); // 解析完整数据包
        }
    }

            // 清除接收中断标志（如果需要手动清除）
        DL_UART_clearInterrupt(UART_BNO080_INST, DL_UART_INTERRUPT_RX);
}




void uart3_init(void)
{
    // 配置中断优先级
    NVIC_SetPriority(UART3_INT_IRQn, 0);  // 设置适当的优先级
    NVIC_ClearPendingIRQ(UART3_INT_IRQn);
    NVIC_EnableIRQ(UART3_INT_IRQn);
}









