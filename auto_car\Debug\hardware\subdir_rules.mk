################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
hardware/%.o: ../hardware/%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"F:/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/auto_car/middle" -I"C:/Users/<USER>/workspace_ccstheia/auto_car/app" -I"C:/Users/<USER>/workspace_ccstheia/auto_car/hardware" -I"C:/Users/<USER>/workspace_ccstheia/auto_car" -I"C:/Users/<USER>/workspace_ccstheia/auto_car/Debug" -I"F:/mspm0_sdk_2_05_00_05/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include" -I"F:/mspm0_sdk_2_05_00_05/mspm0_sdk_2_05_00_05/SCODE" -I"F:/mspm0_sdk_2_05_00_05/mspm0_sdk_2_05_00_05/source" -gdwarf-3 -MMD -MP -MF"hardware/$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


