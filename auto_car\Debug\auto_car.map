******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 30 13:34:09 2025

OUTPUT FILE NAME:   <auto_car.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00004999


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00006fc8  00019038  R  X
  SRAM                  20200000   00008000  00000e69  00007197  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00006fc8   00006fc8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000052a0   000052a0    r-x .text
  00005360    00005360    00001c30   00001c30    r-- .rodata
  00006f90    00006f90    00000038   00000038    r-- .cinit
20200000    20200000    00000c69   00000000    rw-
  20200000    20200000    000007e8   00000000    rw- .bss
  202007e8    202007e8    00000400   00000000    rw- .sysmem
  20200be8    20200be8    00000081   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000052a0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000404     mid_button.o (.text.flex_button_process)
                  00000e94    00000280     empty.o (.text.main)
                  00001114    0000026c     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001380    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  000015a0    000001f4     app_question_task.o (.text.Question_Task_1)
                  00001794    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001970    000001d0     hw_oled.o (.text.OLED_ShowChar)
                  00001b40    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001cd2    00000002     app_question_task.o (.text.Question_Task_2)
                  00001cd4    0000014e     app_sys_mode.o (.text.event_manager)
                  00001e22    00000002     app_question_task.o (.text.Question_Task_3)
                  00001e24    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00001f60    00000120            : _printfi.c.obj (.text._pconv_e)
                  00002080    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  0000218c    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00002290    000000f2     hw_oled.o (.text.OLED_Init)
                  00002382    000000f2     mid_pid.o (.text.pid_calc)
                  00002474    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  0000255c    000000e4     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002640    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002724    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002800    000000dc     hw_encoder.o (.text.GROUP1_IRQHandler)
                  000028dc    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000029b4    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00002a8c    000000d0     mid_button.o (.text.flex_button_read)
                  00002b5c    000000d0     mid_button.o (.text.user_button_init)
                  00002c2c    000000a4     mid_button.o (.text.flex_button_register)
                  00002cd0    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00002d72    00000002     app_question_task.o (.text.Question_Task_4)
                  00002d74    0000009c     hw_oled.o (.text.OLED_Refresh)
                  00002e10    0000009a     hw_oled.o (.text.OLED_ShowString)
                  00002eaa    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002eac    00000090     hw_oled.o (.text.OLED_DrawPoint)
                  00002f3c    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTORL_init)
                  00002fcc    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTORR_init)
                  0000305c    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_SPARE_PWM_init)
                  000030ec    00000090     hw_key.o (.text.key_scan)
                  0000317c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00003208    00000088     hw_motor.o (.text.set_all_motor)
                  00003290    00000084     hw_jy901s.o (.text.__NVIC_SetPriority)
                  00003314    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00003398    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003414    00000078     hw_oled.o (.text.Send_Byte)
                  0000348c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00003500    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00003574    00000074     gray_sensor.o (.text.gray_serial_forward_read)
                  000035e8    0000006c     hw_encoder.o (.text.encoder_update_repeat)
                  00003654    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_BEEP_init)
                  000036bc    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00003724    00000068     hw_encoder.o (.text.encoder_update_continue)
                  0000378c    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  000037f2    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000037f4    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00003858    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000038bc    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000391e    00000002     --HOLE-- [fill = 0]
                  00003920    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00003982    00000002     --HOLE-- [fill = 0]
                  00003984    00000060     hw_oled.o (.text.OLED_Clear)
                  000039e4    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003a40    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00003a98    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003af0    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003b48    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003b9e    00000002     --HOLE-- [fill = 0]
                  00003ba0    00000054     app_speed_pid.o (.text.lowpass_filter)
                  00003bf4    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003c46    00000002     --HOLE-- [fill = 0]
                  00003c48    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00003c98    00000050     app_key_task.o (.text.btn_down_cb)
                  00003ce8    00000050     app_key_task.o (.text.btn_up_cb)
                  00003d38    00000050     app_speed_pid.o (.text.motor_velcity_control)
                  00003d88    0000004e     hw_oled.o (.text.OLED_WR_Byte)
                  00003dd6    00000002     --HOLE-- [fill = 0]
                  00003dd8    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00003e24    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_OLED_UART_init)
                  00003e70    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00003eba    00000002     --HOLE-- [fill = 0]
                  00003ebc    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00003f06    00000002     --HOLE-- [fill = 0]
                  00003f08    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00003f50    00000048     gray_sensor.o (.text.NumofZero)
                  00003f98    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00003fe0    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_BNO080_init)
                  00004028    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  0000406c    00000044     hw_oled.o (.text.I2C_Start)
                  000040b0    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000040f0    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004130    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00004170    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000041b0    00000040     hw_motor.o (.text.restrict_pwm_max_value)
                  000041f0    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000422c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00004268    0000003c     hw_oled.o (.text.I2C_WaitAck)
                  000042a4    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_TICK_init)
                  000042e0    0000003c     mid_timer.o (.text.TIMA0_IRQHandler)
                  0000431c    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00004358    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00004394    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000043d0    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000440a    00000002     --HOLE-- [fill = 0]
                  0000440c    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00004446    0000003a     mid_pid.o (.text.pid_init)
                  00004480    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  000044b8    00000038     app_speed_pid.o (.text.speed_pid_init)
                  000044f0    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00004528    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000455c    00000034     hw_oled.o (.text.I2C_Stop)
                  00004590    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_POWER_ADC_init)
                  000045c4    00000034     gray_sensor.o (.text.extractSensorData)
                  000045f8    00000030     uart.o (.text.UART0_IRQHandler)
                  00004628    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00004658    00000030     app_key_task.o (.text.btn_mid_cb)
                  00004688    00000030     app_key_task.o (.text.btn_right_cb)
                  000046b8    00000030     mid_remind.o (.text.remind_update)
                  000046e8    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_SERVO_IIC_init)
                  00004714    0000002c     hw_encoder.o (.text.__NVIC_ClearPendingIRQ)
                  00004740    0000002c     hw_jy901s.o (.text.__NVIC_ClearPendingIRQ)
                  0000476c    0000002c     mid_timer.o (.text.__NVIC_ClearPendingIRQ)
                  00004798    0000002c     uart.o (.text.__NVIC_ClearPendingIRQ)
                  000047c4    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  000047f0    0000002c     hw_encoder.o (.text.__NVIC_EnableIRQ)
                  0000481c    0000002c     hw_jy901s.o (.text.__NVIC_EnableIRQ)
                  00004848    0000002c     mid_timer.o (.text.__NVIC_EnableIRQ)
                  00004874    0000002c     uart.o (.text.__NVIC_EnableIRQ)
                  000048a0    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000048cc    0000002c     uart.o (.text.uart0_send_char)
                  000048f8    00000028     hw_jy901s.o (.text.BNO080_GetData)
                  00004920    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00004948    00000028     hw_motor.o (.text.set_left_motor)
                  00004970    00000028     hw_motor.o (.text.set_right_motor)
                  00004998    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000049c0    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  000049e6    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00004a0c    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00004a30    00000024     app_key_task.o (.text.btn_left_cb)
                  00004a54    00000022     hw_encoder.o (.text.encoder_init)
                  00004a76    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00004a98    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00004ab8    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00004ad8    00000020     delay.o (.text.SysTick_Handler)
                  00004af8    00000020     hw_motor.o (.text.stop_motor)
                  00004b18    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00004b36    00000002     --HOLE-- [fill = 0]
                  00004b38    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00004b56    0000001e     hw_jy901s.o (.text.uart3_init)
                  00004b74    0000001c     hw_encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00004b90    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00004bac    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00004bc8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00004be4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInput)
                  00004c00    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00004c1c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00004c38    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00004c54    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00004c70    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00004c8c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00004ca8    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00004cc4    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00004ce0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00004cf8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00004d10    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00004d28    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00004d40    00000018     hw_encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00004d58    00000018     empty.o (.text.DL_GPIO_initDigitalOutput)
                  00004d70    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00004d88    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00004da0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00004db8    00000018     gray_sensor.o (.text.DL_GPIO_setPins)
                  00004dd0    00000018     hw_oled.o (.text.DL_GPIO_setPins)
                  00004de8    00000018     hw_remind.o (.text.DL_GPIO_setPins)
                  00004e00    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00004e18    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00004e30    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00004e48    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00004e60    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00004e78    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00004e90    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00004ea8    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00004ec0    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00004ed8    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00004ef0    00000018     uart.o (.text.DL_UART_isBusy)
                  00004f08    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00004f20    00000018     libc.a : sprintf.c.obj (.text._outs)
                  00004f38    00000018     hw_remind.o (.text.beep_off)
                  00004f50    00000018     hw_remind.o (.text.beep_on)
                  00004f68    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00004f7e    00000016     gray_sensor.o (.text.DL_GPIO_readPins)
                  00004f94    00000016     hw_encoder.o (.text.DL_GPIO_readPins)
                  00004faa    00000016     hw_key.o (.text.DL_GPIO_readPins)
                  00004fc0    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00004fd6    00000016     uart.o (.text.DL_UART_transmitData)
                  00004fec    00000016     mid_button.o (.text.button_down_read)
                  00005002    00000016     mid_button.o (.text.button_left_read)
                  00005018    00000016     mid_button.o (.text.button_mid_read)
                  0000502e    00000016     mid_button.o (.text.button_right_read)
                  00005044    00000016     mid_button.o (.text.button_up_read)
                  0000505a    00000016     mid_timer.o (.text.timer_init)
                  00005070    00000016     uart.o (.text.uart_init)
                  00005086    00000014     gray_sensor.o (.text.DL_GPIO_clearPins)
                  0000509a    00000014     hw_oled.o (.text.DL_GPIO_clearPins)
                  000050ae    00000014     hw_remind.o (.text.DL_GPIO_clearPins)
                  000050c2    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000050d6    00000002     --HOLE-- [fill = 0]
                  000050d8    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000050ec    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_enableMFCLK)
                  00005100    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00005114    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00005128    00000014     uart.o (.text.DL_UART_receiveData)
                  0000513c    00000014     app_question_task.o (.text.State_Machine_init)
                  00005150    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00005164    00000014     hw_remind.o (.text.led_off)
                  00005178    00000014     hw_remind.o (.text.led_on)
                  0000518c    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000051a0    00000012     mid_timer.o (.text.DL_Timer_getPendingInterrupt)
                  000051b2    00000012     uart.o (.text.DL_UART_getPendingInterrupt)
                  000051c4    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000051d6    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000051e8    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000051fa    00000002     --HOLE-- [fill = 0]
                  000051fc    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  0000520c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  0000521c    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  0000522c    00000010     app_sys_mode.o (.text.sys_event_init)
                  0000523c    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  0000524c    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  0000525c    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000526a    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00005278    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00005286    00000002     --HOLE-- [fill = 0]
                  00005288    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00005294    0000000c     mid_button.o (.text.flex_button_scan)
                  000052a0    0000000c     app_sys_mode.o (.text.get_functional_mode)
                  000052ac    0000000c     hw_encoder.o (.text.get_left_encoder)
                  000052b8    0000000c     hw_encoder.o (.text.get_right_encoder)
                  000052c4    0000000c     app_speed_pid.o (.text.get_speed_pid_target)
                  000052d0    0000000c     mid_timer.o (.text.get_task_status)
                  000052dc    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000052e6    0000000a     hw_oled.o (.text.IIC_delay)
                  000052f0    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000052fa    00000002     --HOLE-- [fill = 0]
                  000052fc    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  0000530c    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005316    0000000a            : sprintf.c.obj (.text._outc)
                  00005320    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00005328    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00005330    00000006     libc.a : exit.c.obj (.text:abort)
                  00005336    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000533a    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  0000533e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00005342    00000002     --HOLE-- [fill = 0]
                  00005344    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00005354    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00005358    00000008     --HOLE-- [fill = 0]

.cinit     0    00006f90    00000038     
                  00006f90    0000000f     (.cinit..data.load) [load image, compression = lzss]
                  00006f9f    00000001     --HOLE-- [fill = 0]
                  00006fa0    0000000c     (__TI_handler_table)
                  00006fac    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00006fb4    00000010     (__TI_cinit_table)
                  00006fc4    00000004     --HOLE-- [fill = 0]

.rodata    0    00005360    00001c30     
                  00005360    00000d5c     hw_oled.o (.rodata.asc2_2412)
                  000060bc    000005f0     hw_oled.o (.rodata.asc2_1608)
                  000066ac    00000474     hw_oled.o (.rodata.asc2_1206)
                  00006b20    00000228     hw_oled.o (.rodata.asc2_0806)
                  00006d48    00000008     ti_msp_dl_config.o (.rodata.gPOWER_ADCClockConfig)
                  00006d50    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00006e51    00000003     ti_msp_dl_config.o (.rodata.gPWM_BEEPClockConfig)
                  00006e54    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00006e7c    00000015     empty.o (.rodata.str1.17669528882079347314.1)
                  00006e91    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTORLClockConfig)
                  00006e94    00000014     ti_msp_dl_config.o (.rodata.gTIMER_TICKTimerConfig)
                  00006ea8    00000012     app_question_task.o (.rodata.str1.16799214272990356510.1)
                  00006eba    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00006ecb    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00006edc    00000010     empty.o (.rodata.str1.9517790425240694019.1)
                  00006eec    0000000d     empty.o (.rodata.str1.14685083708502177989.1)
                  00006ef9    0000000d     app_question_task.o (.rodata.str1.4734725452787746387.1)
                  00006f06    0000000b     app_question_task.o (.rodata.str1.2015821740806606305.1)
                  00006f11    0000000b     empty.o (.rodata.str1.254342170260855183.1)
                  00006f1c    0000000a     ti_msp_dl_config.o (.rodata.gOLED_UARTConfig)
                  00006f26    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00006f30    0000000a     ti_msp_dl_config.o (.rodata.gUART_BNO080Config)
                  00006f3a    00000002     ti_msp_dl_config.o (.rodata.gOLED_UARTClockConfig)
                  00006f3c    00000008     ti_msp_dl_config.o (.rodata.gPWM_BEEPConfig)
                  00006f44    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTORLConfig)
                  00006f4c    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTORRConfig)
                  00006f54    00000008     ti_msp_dl_config.o (.rodata.gSPARE_PWMConfig)
                  00006f5c    00000008     app_question_task.o (.rodata.str1.10290795302062506517.1)
                  00006f64    00000008     empty.o (.rodata.str1.11898133897667081452.1)
                  00006f6c    00000008     app_question_task.o (.rodata.str1.6366099510686936362.1)
                  00006f74    00000008     empty.o (.rodata.str1.7401042497206923953.1)
                  00006f7c    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTORRClockConfig)
                  00006f7f    00000003     ti_msp_dl_config.o (.rodata.gSPARE_PWMClockConfig)
                  00006f82    00000003     ti_msp_dl_config.o (.rodata.gTIMER_TICKClockConfig)
                  00006f85    00000002     ti_msp_dl_config.o (.rodata.gSERVO_IICClockConfig)
                  00006f87    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00006f89    00000002     ti_msp_dl_config.o (.rodata.gUART_BNO080ClockConfig)
                  00006f8b    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000007e8     UNINITIALIZED
                  20200000    00000480     (.common:OLED_GRAM)
                  20200480    000000bc     (.common:gPWM_MOTORRBackup)
                  2020053c    000000bc     (.common:gTIMER_TICKBackup)
                  202005f8    000000a0     (.common:gPWM_BEEPBackup)
                  20200698    0000008c     mid_button.o (.bss.user_button)
                  20200724    00000030     (.common:gUART_BNO080Backup)
                  20200754    00000030     (.common:speed_pid)
                  20200784    00000014     (.common:State_Machine)
                  20200798    00000010     hw_encoder.o (.bss.left_motor_encoder)
                  202007a8    00000010     hw_encoder.o (.bss.right_motor_encoder)
                  202007b8    00000010     (.common:BNO080_Data)
                  202007c8    00000010     (.common:system_status)
                  202007d8    00000004     hw_encoder.o (.bss.gpio_interrup1)
                  202007dc    00000004     hw_encoder.o (.bss.gpio_interrup2)
                  202007e0    00000004     (.common:left_pwm)
                  202007e4    00000004     (.common:right_pwm)

.sysmem    0    202007e8    00000400     UNINITIALIZED
                  202007e8    00000010     libc.a : memory.c.obj (.sysmem)
                  202007f8    000003f0     --HOLE--

.data      0    20200be8    00000081     UNINITIALIZED
                  20200be8    0000003c     empty.o (.data.buff)
                  20200c24    00000010     hw_jy901s.o (.data.g_bno080_data)
                  20200c34    00000008     empty.o (.data.sensor)
                  20200c3c    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200c40    00000004     mid_button.o (.data.btn_head)
                  20200c44    00000004     delay.o (.data.delay_times)
                  20200c48    00000004     app_speed_pid.o (.data.filtered_speed)
                  20200c4c    00000004     hw_encoder.o (.data.odometry_sum)
                  20200c50    00000004     app_question_task.o (.data.q1_first_flag)
                  20200c54    00000004     mid_timer.o (.data.system_time)
                  20200c58    00000002     mid_button.o (.data.cont)
                  20200c5a    00000002     mid_button.o (.data.key_rst_data)
                  20200c5c    00000002     mid_button.o (.data.keydata)
                  20200c5e    00000002     mid_button.o (.data.trg)
                  20200c60    00000001     mid_button.o (.data.button_cnt)
                  20200c61    00000001     app_key_task.o (.data.key_1_flag)
                  20200c62    00000001     app_key_task.o (.data.key_2_flag)
                  20200c63    00000001     app_key_task.o (.data.key_3_flag)
                  20200c64    00000001     app_key_task.o (.data.key_4_flag)
                  20200c65    00000001     mid_remind.o (.data.remind_flag)
                  20200c66    00000001     empty.o (.data.sensor_data)
                  20200c67    00000001     mid_timer.o (.data.tack_enable_flag)
                  20200c68    00000001     uart.o (.data.uart_data)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3518    153       584    
       empty.o                        708     77        101    
       startup_mspm0g350x_ticlang.o   8       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4234    422       685    
                                                               
    .\SCODE\
       uart.o                         286     0         1      
       delay.o                        32      0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         318     0         5      
                                                               
    .\app\
       app_question_task.o            526     58        24     
       app_sys_mode.o                 362     0         0      
       app_key_task.o                 292     0         4      
       app_speed_pid.o                232     0         52     
       app_angle_control.o            0       0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         1412    58        88     
                                                               
    .\hardware\
       hw_oled.o                      1688    6632      1152   
       hw_encoder.o                   652     0         44     
       hw_motor.o                     312     0         0      
       gray_sensor.o                  306     0         0      
       hw_jy901s.o                    290     0         16     
       hw_key.o                       166     0         0      
       hw_remind.o                    132     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3546    6632      1212   
                                                               
    .\middle\
       mid_button.o                   1730    0         153    
       mid_pid.o                      300     0         0      
       mid_timer.o                    200     0         5      
       mid_remind.o                   48      0         1      
    +--+------------------------------+-------+---------+---------+
       Total:                         2278    0         159    
                                                               
    F:/mspm0_sdk_2_05_00_05/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_uart.o                      90      0         0      
       dl_adc12.o                     64      0         0      
       dl_i2c.o                       38      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1178    0         0      
                                                               
    F:\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5434    291       4      
                                                               
    F:\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    F:\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2712    0         0      
                                                               
       Heap:                          0       0         1024   
       Stack:                         0       0         512    
       Linker Generated:              0       51        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   21116   7454      3689   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00006fb4 records: 2, size/record: 8, table size: 16
	.data: load addr=00006f90, load size=0000000f bytes, run addr=20200be8, run size=00000081 bytes, compression=lzss
	.bss: load addr=00006fac, load size=00000008 bytes, run addr=20200000, run size=000007e8 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00006fa0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001b41     000052fc     000052f8   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00004999     00005344     0000533e   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00005337  ADC0_IRQHandler                      
00005337  ADC1_IRQHandler                      
00005337  AES_IRQHandler                       
202007b8  BNO080_Data                          
000048f9  BNO080_GetData                       
0000533a  C$$EXIT                              
00005337  CANFD0_IRQHandler                    
00005337  DAC0_IRQHandler                      
000040b1  DL_ADC12_setClockConfig              
000052dd  DL_Common_delayCycles                
000049e7  DL_I2C_setClockConfig                
00002725  DL_SYSCTL_configSYSPLL               
000037f5  DL_SYSCTL_setHFCLKSourceHFXTParams   
00004029  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000218d  DL_Timer_initFourCCPWMMode           
00002475  DL_Timer_initTimerMode               
00004c8d  DL_Timer_setCaptCompUpdateMethod     
00004ec1  DL_Timer_setCaptureCompareOutCtl     
0000520d  DL_Timer_setCaptureCompareValue      
00004ca9  DL_Timer_setClockConfig              
00003f09  DL_UART_init                         
000051c5  DL_UART_setClockConfig               
00005337  DMA_IRQHandler                       
00005337  Default_Handler                      
00005337  GROUP0_IRQHandler                    
00002801  GROUP1_IRQHandler                    
0000533b  HOSTexit                             
00005337  HardFault_Handler                    
00005337  I2C0_IRQHandler                      
00005337  I2C1_IRQHandler                      
00005337  NMI_Handler                          
00003f51  NumofZero                            
00003985  OLED_Clear                           
00002ead  OLED_DrawPoint                       
20200000  OLED_GRAM                            
00002291  OLED_Init                            
00002d75  OLED_Refresh                         
00001971  OLED_ShowChar                        
00002e11  OLED_ShowString                      
00005337  PendSV_Handler                       
000015a1  Question_Task_1                      
00001cd3  Question_Task_2                      
00001e23  Question_Task_3                      
00002d73  Question_Task_4                      
00005337  RTC_IRQHandler                       
0000533f  Reset_Handler                        
00005337  SPI0_IRQHandler                      
00005337  SPI1_IRQHandler                      
00005337  SVC_Handler                          
00001115  SYSCFG_DL_GPIO_init                  
00003e25  SYSCFG_DL_OLED_UART_init             
00004591  SYSCFG_DL_POWER_ADC_init             
00003655  SYSCFG_DL_PWM_BEEP_init              
00002f3d  SYSCFG_DL_PWM_MOTORL_init            
00002fcd  SYSCFG_DL_PWM_MOTORR_init            
000046e9  SYSCFG_DL_SERVO_IIC_init             
0000305d  SYSCFG_DL_SPARE_PWM_init             
00003a41  SYSCFG_DL_SYSCTL_init                
0000521d  SYSCFG_DL_SYSTICK_init               
000042a5  SYSCFG_DL_TIMER_TICK_init            
00003f99  SYSCFG_DL_UART_0_init                
00003fe1  SYSCFG_DL_UART_BNO080_init           
00003859  SYSCFG_DL_init                       
0000255d  SYSCFG_DL_initPower                  
20200784  State_Machine                        
0000513d  State_Machine_init                   
00004ad9  SysTick_Handler                      
000042e1  TIMA0_IRQHandler                     
00005337  TIMA1_IRQHandler                     
00005337  TIMG0_IRQHandler                     
00005337  TIMG12_IRQHandler                    
00005337  TIMG6_IRQHandler                     
00005337  TIMG7_IRQHandler                     
00005337  TIMG8_IRQHandler                     
000051d7  TI_memcpy_small                      
00005279  TI_memset_small                      
000045f9  UART0_IRQHandler                     
00005337  UART1_IRQHandler                     
00005337  UART2_IRQHandler                     
00005337  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00006fb4  __TI_CINIT_Base                      
00006fc4  __TI_CINIT_Limit                     
00006fc4  __TI_CINIT_Warm                      
00006fa0  __TI_Handler_Table_Base              
00006fac  __TI_Handler_Table_Limit             
00004395  __TI_auto_init_nobinit_nopinit       
00003399  __TI_decompress_lzss                 
000051e9  __TI_decompress_none                 
00003a99  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
0000524d  __TI_zero_init                       
00001b4b  __adddf3                             
000029bf  __addsf3                             
00006d50  __aeabi_ctype_table_                 
00006d50  __aeabi_ctype_table_C                
00003501  __aeabi_d2f                          
00003ebd  __aeabi_d2iz                         
00001b4b  __aeabi_dadd                         
000038bd  __aeabi_dcmpeq                       
000038f9  __aeabi_dcmpge                       
0000390d  __aeabi_dcmpgt                       
000038e5  __aeabi_dcmple                       
000038d1  __aeabi_dcmplt                       
00002081  __aeabi_ddiv                         
00002641  __aeabi_dmul                         
00001b41  __aeabi_dsub                         
20200c3c  __aeabi_errno                        
00005321  __aeabi_errno_addr                   
00004131  __aeabi_f2d                          
00004481  __aeabi_f2iz                         
000029bf  __aeabi_fadd                         
00003921  __aeabi_fcmpeq                       
0000395d  __aeabi_fcmpge                       
00003971  __aeabi_fcmpgt                       
00003949  __aeabi_fcmple                       
00003935  __aeabi_fcmplt                       
0000317d  __aeabi_fmul                         
000029b5  __aeabi_fsub                         
000048a1  __aeabi_i2d                          
0000431d  __aeabi_i2f                          
00003b49  __aeabi_idiv                         
00002eab  __aeabi_idiv0                        
00003b49  __aeabi_idivmod                      
000037f3  __aeabi_ldiv0                        
00004b39  __aeabi_llsl                         
00004a0d  __aeabi_lmul                         
00005289  __aeabi_memclr                       
00005289  __aeabi_memclr4                      
00005289  __aeabi_memclr8                      
00005329  __aeabi_memcpy                       
00005329  __aeabi_memcpy4                      
00005329  __aeabi_memcpy8                      
0000525d  __aeabi_memset                       
0000525d  __aeabi_memset4                      
0000525d  __aeabi_memset8                      
000040f1  __aeabi_uidiv                        
000040f1  __aeabi_uidivmod                     
00005151  __aeabi_uldivmod                     
00004b39  __ashldi3                            
ffffffff  __binit__                            
000036bd  __cmpdf2                             
000043d1  __cmpsf2                             
00002081  __divdf3                             
000036bd  __eqdf2                              
000043d1  __eqsf2                              
00004131  __extendsfdf2                        
00003ebd  __fixdfsi                            
00004481  __fixsfsi                            
000048a1  __floatsidf                          
0000431d  __floatsisf                          
0000348d  __gedf2                              
00004359  __gesf2                              
0000348d  __gtdf2                              
00004359  __gtsf2                              
000036bd  __ledf2                              
000043d1  __lesf2                              
000036bd  __ltdf2                              
000043d1  __ltsf2                              
UNDEFED   __mpu_init                           
00002641  __muldf3                             
00004a0d  __muldi3                             
0000440d  __muldsi3                            
0000317d  __mulsf3                             
000036bd  __nedf2                              
000043d1  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001b41  __subdf3                             
000029b5  __subsf3                             
00003501  __truncdfsf2                         
00002cd1  __udivmoddi4                         
00004999  _c_int00_noargs                      
202007e8  _sys_memory                          
UNDEFED   _system_post_cinit                   
00005355  _system_pre_init                     
00005331  abort                                
00006b20  asc2_0806                            
000066ac  asc2_1206                            
000060bc  asc2_1608                            
00005360  asc2_2412                            
00004171  atoi                                 
00004f39  beep_off                             
00004f51  beep_on                              
ffffffff  binit                                
00003c99  btn_down_cb                          
00004a31  btn_left_cb                          
00004659  btn_mid_cb                           
00004689  btn_right_cb                         
00003ce9  btn_up_cb                            
20200be8  buff                                 
20200c44  delay_times                          
00004a55  encoder_init                         
00003725  encoder_update_continue              
000035e9  encoder_update_repeat                
00001cd5  event_manager                        
000045c5  extractSensorData                    
20200c48  filtered_speed                       
00002c2d  flex_button_register                 
00005295  flex_button_scan                     
000039e5  frexp                                
000039e5  frexpl                               
202005f8  gPWM_BEEPBackup                      
20200480  gPWM_MOTORRBackup                    
2020053c  gTIMER_TICKBackup                    
20200724  gUART_BNO080Backup                   
000052a1  get_functional_mode                  
000052ad  get_left_encoder                     
000052b9  get_right_encoder                    
000052c5  get_speed_pid_target                 
000052d1  get_task_status                      
00003575  gray_serial_forward_read             
00000000  interruptVectors                     
20200c61  key_1_flag                           
20200c62  key_2_flag                           
20200c63  key_3_flag                           
20200c64  key_4_flag                           
000030ed  key_scan                             
000028dd  ldexp                                
000028dd  ldexpl                               
00005165  led_off                              
00005179  led_on                               
202007e0  left_pwm                             
00003ba1  lowpass_filter                       
00000e95  main                                 
00004a77  memccpy                              
00003d39  motor_velcity_control                
20200c4c  odometry_sum                         
00002383  pid_calc                             
00004447  pid_init                             
20200c50  q1_first_flag                        
20200c65  remind_flag                          
000046b9  remind_update                        
202007e4  right_pwm                            
000028dd  scalbn                               
000028dd  scalbnl                              
20200c34  sensor                               
20200c66  sensor_data                          
00003209  set_all_motor                        
20200754  speed_pid                            
000044b9  speed_pid_init                       
000044f1  sprintf                              
00004af9  stop_motor                           
0000522d  sys_event_init                       
202007c8  system_status                        
20200c54  system_time                          
0000505b  timer_init                           
000048cd  uart0_send_char                      
00004b57  uart3_init                           
20200c68  uart_data                            
00005071  uart_init                            
00002b5d  user_button_init                     
0000523d  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000e95  main                                 
00001115  SYSCFG_DL_GPIO_init                  
000015a1  Question_Task_1                      
00001971  OLED_ShowChar                        
00001b41  __aeabi_dsub                         
00001b41  __subdf3                             
00001b4b  __adddf3                             
00001b4b  __aeabi_dadd                         
00001cd3  Question_Task_2                      
00001cd5  event_manager                        
00001e23  Question_Task_3                      
00002081  __aeabi_ddiv                         
00002081  __divdf3                             
0000218d  DL_Timer_initFourCCPWMMode           
00002291  OLED_Init                            
00002383  pid_calc                             
00002475  DL_Timer_initTimerMode               
0000255d  SYSCFG_DL_initPower                  
00002641  __aeabi_dmul                         
00002641  __muldf3                             
00002725  DL_SYSCTL_configSYSPLL               
00002801  GROUP1_IRQHandler                    
000028dd  ldexp                                
000028dd  ldexpl                               
000028dd  scalbn                               
000028dd  scalbnl                              
000029b5  __aeabi_fsub                         
000029b5  __subsf3                             
000029bf  __addsf3                             
000029bf  __aeabi_fadd                         
00002b5d  user_button_init                     
00002c2d  flex_button_register                 
00002cd1  __udivmoddi4                         
00002d73  Question_Task_4                      
00002d75  OLED_Refresh                         
00002e11  OLED_ShowString                      
00002eab  __aeabi_idiv0                        
00002ead  OLED_DrawPoint                       
00002f3d  SYSCFG_DL_PWM_MOTORL_init            
00002fcd  SYSCFG_DL_PWM_MOTORR_init            
0000305d  SYSCFG_DL_SPARE_PWM_init             
000030ed  key_scan                             
0000317d  __aeabi_fmul                         
0000317d  __mulsf3                             
00003209  set_all_motor                        
00003399  __TI_decompress_lzss                 
0000348d  __gedf2                              
0000348d  __gtdf2                              
00003501  __aeabi_d2f                          
00003501  __truncdfsf2                         
00003575  gray_serial_forward_read             
000035e9  encoder_update_repeat                
00003655  SYSCFG_DL_PWM_BEEP_init              
000036bd  __cmpdf2                             
000036bd  __eqdf2                              
000036bd  __ledf2                              
000036bd  __ltdf2                              
000036bd  __nedf2                              
00003725  encoder_update_continue              
000037f3  __aeabi_ldiv0                        
000037f5  DL_SYSCTL_setHFCLKSourceHFXTParams   
00003859  SYSCFG_DL_init                       
000038bd  __aeabi_dcmpeq                       
000038d1  __aeabi_dcmplt                       
000038e5  __aeabi_dcmple                       
000038f9  __aeabi_dcmpge                       
0000390d  __aeabi_dcmpgt                       
00003921  __aeabi_fcmpeq                       
00003935  __aeabi_fcmplt                       
00003949  __aeabi_fcmple                       
0000395d  __aeabi_fcmpge                       
00003971  __aeabi_fcmpgt                       
00003985  OLED_Clear                           
000039e5  frexp                                
000039e5  frexpl                               
00003a41  SYSCFG_DL_SYSCTL_init                
00003a99  __TI_ltoa                            
00003b49  __aeabi_idiv                         
00003b49  __aeabi_idivmod                      
00003ba1  lowpass_filter                       
00003c99  btn_down_cb                          
00003ce9  btn_up_cb                            
00003d39  motor_velcity_control                
00003e25  SYSCFG_DL_OLED_UART_init             
00003ebd  __aeabi_d2iz                         
00003ebd  __fixdfsi                            
00003f09  DL_UART_init                         
00003f51  NumofZero                            
00003f99  SYSCFG_DL_UART_0_init                
00003fe1  SYSCFG_DL_UART_BNO080_init           
00004029  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000040b1  DL_ADC12_setClockConfig              
000040f1  __aeabi_uidiv                        
000040f1  __aeabi_uidivmod                     
00004131  __aeabi_f2d                          
00004131  __extendsfdf2                        
00004171  atoi                                 
000042a5  SYSCFG_DL_TIMER_TICK_init            
000042e1  TIMA0_IRQHandler                     
0000431d  __aeabi_i2f                          
0000431d  __floatsisf                          
00004359  __gesf2                              
00004359  __gtsf2                              
00004395  __TI_auto_init_nobinit_nopinit       
000043d1  __cmpsf2                             
000043d1  __eqsf2                              
000043d1  __lesf2                              
000043d1  __ltsf2                              
000043d1  __nesf2                              
0000440d  __muldsi3                            
00004447  pid_init                             
00004481  __aeabi_f2iz                         
00004481  __fixsfsi                            
000044b9  speed_pid_init                       
000044f1  sprintf                              
00004591  SYSCFG_DL_POWER_ADC_init             
000045c5  extractSensorData                    
000045f9  UART0_IRQHandler                     
00004659  btn_mid_cb                           
00004689  btn_right_cb                         
000046b9  remind_update                        
000046e9  SYSCFG_DL_SERVO_IIC_init             
000048a1  __aeabi_i2d                          
000048a1  __floatsidf                          
000048cd  uart0_send_char                      
000048f9  BNO080_GetData                       
00004999  _c_int00_noargs                      
000049e7  DL_I2C_setClockConfig                
00004a0d  __aeabi_lmul                         
00004a0d  __muldi3                             
00004a31  btn_left_cb                          
00004a55  encoder_init                         
00004a77  memccpy                              
00004ad9  SysTick_Handler                      
00004af9  stop_motor                           
00004b39  __aeabi_llsl                         
00004b39  __ashldi3                            
00004b57  uart3_init                           
00004c8d  DL_Timer_setCaptCompUpdateMethod     
00004ca9  DL_Timer_setClockConfig              
00004ec1  DL_Timer_setCaptureCompareOutCtl     
00004f39  beep_off                             
00004f51  beep_on                              
0000505b  timer_init                           
00005071  uart_init                            
0000513d  State_Machine_init                   
00005151  __aeabi_uldivmod                     
00005165  led_off                              
00005179  led_on                               
000051c5  DL_UART_setClockConfig               
000051d7  TI_memcpy_small                      
000051e9  __TI_decompress_none                 
0000520d  DL_Timer_setCaptureCompareValue      
0000521d  SYSCFG_DL_SYSTICK_init               
0000522d  sys_event_init                       
0000523d  wcslen                               
0000524d  __TI_zero_init                       
0000525d  __aeabi_memset                       
0000525d  __aeabi_memset4                      
0000525d  __aeabi_memset8                      
00005279  TI_memset_small                      
00005289  __aeabi_memclr                       
00005289  __aeabi_memclr4                      
00005289  __aeabi_memclr8                      
00005295  flex_button_scan                     
000052a1  get_functional_mode                  
000052ad  get_left_encoder                     
000052b9  get_right_encoder                    
000052c5  get_speed_pid_target                 
000052d1  get_task_status                      
000052dd  DL_Common_delayCycles                
00005321  __aeabi_errno_addr                   
00005329  __aeabi_memcpy                       
00005329  __aeabi_memcpy4                      
00005329  __aeabi_memcpy8                      
00005331  abort                                
00005337  ADC0_IRQHandler                      
00005337  ADC1_IRQHandler                      
00005337  AES_IRQHandler                       
00005337  CANFD0_IRQHandler                    
00005337  DAC0_IRQHandler                      
00005337  DMA_IRQHandler                       
00005337  Default_Handler                      
00005337  GROUP0_IRQHandler                    
00005337  HardFault_Handler                    
00005337  I2C0_IRQHandler                      
00005337  I2C1_IRQHandler                      
00005337  NMI_Handler                          
00005337  PendSV_Handler                       
00005337  RTC_IRQHandler                       
00005337  SPI0_IRQHandler                      
00005337  SPI1_IRQHandler                      
00005337  SVC_Handler                          
00005337  TIMA1_IRQHandler                     
00005337  TIMG0_IRQHandler                     
00005337  TIMG12_IRQHandler                    
00005337  TIMG6_IRQHandler                     
00005337  TIMG7_IRQHandler                     
00005337  TIMG8_IRQHandler                     
00005337  UART1_IRQHandler                     
00005337  UART2_IRQHandler                     
00005337  UART3_IRQHandler                     
0000533a  C$$EXIT                              
0000533b  HOSTexit                             
0000533f  Reset_Handler                        
00005355  _system_pre_init                     
00005360  asc2_2412                            
000060bc  asc2_1608                            
000066ac  asc2_1206                            
00006b20  asc2_0806                            
00006d50  __aeabi_ctype_table_                 
00006d50  __aeabi_ctype_table_C                
00006fa0  __TI_Handler_Table_Base              
00006fac  __TI_Handler_Table_Limit             
00006fb4  __TI_CINIT_Base                      
00006fc4  __TI_CINIT_Limit                     
00006fc4  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200480  gPWM_MOTORRBackup                    
2020053c  gTIMER_TICKBackup                    
202005f8  gPWM_BEEPBackup                      
20200724  gUART_BNO080Backup                   
20200754  speed_pid                            
20200784  State_Machine                        
202007b8  BNO080_Data                          
202007c8  system_status                        
202007e0  left_pwm                             
202007e4  right_pwm                            
202007e8  _sys_memory                          
20200be8  buff                                 
20200c34  sensor                               
20200c3c  __aeabi_errno                        
20200c44  delay_times                          
20200c48  filtered_speed                       
20200c4c  odometry_sum                         
20200c50  q1_first_flag                        
20200c54  system_time                          
20200c61  key_1_flag                           
20200c62  key_2_flag                           
20200c63  key_3_flag                           
20200c64  key_4_flag                           
20200c65  remind_flag                          
20200c66  sensor_data                          
20200c68  uart_data                            
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[271 symbols]
