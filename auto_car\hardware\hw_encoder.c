#include "hw_encoder.h"

static volatile uint32_t gpio_interrup1, gpio_interrup2;
static ENCODER_RES left_motor_encoder;
static ENCODER_RES right_motor_encoder;

int odometry_sum = 0;                      //里程数

//仅开启中断
void encoder_init(void)
{
	//编码器引脚外部中断
	NVIC_ClearPendingIRQ(GPIO_ENCODER_GPIOB_INT_IRQN);
	NVIC_ClearPendingIRQ(GPIO_ENCODER_GPIOA_INT_IRQN);
	NVIC_EnableIRQ(GPIO_ENCODER_GPIOB_INT_IRQN);
	NVIC_EnableIRQ(GPIO_ENCODER_GPIOA_INT_IRQN);
}

int get_left_encoder(void)
{
	return left_motor_encoder.count;
}
int get_right_encoder(void)
{
	return right_motor_encoder.count;
}
int get_temp_left_encoder(void)
{
	return left_motor_encoder.temp_count;
}
int get_temp_right_encoder(void)
{
	return right_motor_encoder.temp_count;
}

//编码器数据更新
//请间隔一定时间更新
void encoder_update_repeat(void)
{
	left_motor_encoder.count = left_motor_encoder.temp_count;
    right_motor_encoder.count = right_motor_encoder.temp_count;
	//确定方向
	left_motor_encoder.dir = (left_motor_encoder.count > 0) ? FORWARD : REVERSAL;
    right_motor_encoder.dir = (right_motor_encoder.count > 0) ? FORWARD : REVERSAL;

    //编码器计数值清零
	left_motor_encoder.temp_count = 0;
    right_motor_encoder.temp_count = 0;

	//总里程数
	odometry_sum += left_motor_encoder.count;

}


void encoder_update_continue(void)
{
	left_motor_encoder.count = left_motor_encoder.count + left_motor_encoder.temp_count;
	right_motor_encoder.count = right_motor_encoder.count + right_motor_encoder.temp_count;
	//确定方向
	left_motor_encoder.dir = (left_motor_encoder.count > 0) ? FORWARD : REVERSAL;
    right_motor_encoder.dir = (right_motor_encoder.count > 0) ? FORWARD : REVERSAL;

    //编码器计数值清零
	left_motor_encoder.temp_count = 0;
    right_motor_encoder.temp_count = 0;

}

/*******************************************************
函数功能：外部中断模拟编码器信号
入口函数：无
返回  值：无
***********************************************************/
void Exti_IRQHandler(void)
{
	//获取中断信号
	gpio_interrup1 = DL_GPIO_getEnabledInterruptStatus(GPIOB, GPIO_ENCODER_PIN_LA_PIN);
	gpio_interrup2 = DL_GPIO_getEnabledInterruptStatus(GPIOA, GPIO_ENCODER_PIN_RA_PIN);
	//encoder LA
	if((gpio_interrup1 & GPIO_ENCODER_PIN_LA_PIN)==GPIO_ENCODER_PIN_LA_PIN)
	{
		if(!DL_GPIO_readPins(GPIOB,GPIO_ENCODER_PIN_LB_PIN))
		{
			left_motor_encoder.temp_count++;
		}
		else
		{
			left_motor_encoder.temp_count--;
		}
	}
	//encoder RA
	if((gpio_interrup2 & GPIO_ENCODER_PIN_RA_PIN)==GPIO_ENCODER_PIN_RA_PIN)
	{
		if(!DL_GPIO_readPins(GPIOB,GPIO_ENCODER_PIN_RB_PIN))
		{
			right_motor_encoder.temp_count--;
		}
		else
		{
			right_motor_encoder.temp_count++;
		}
	}

	//清除状态
	DL_GPIO_clearInterruptStatus(GPIOB, GPIO_ENCODER_PIN_LA_PIN);
	DL_GPIO_clearInterruptStatus(GPIOA, GPIO_ENCODER_PIN_RA_PIN);
}
