################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
%.o: ../%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"F:/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/auto_car/middle" -I"C:/Users/<USER>/workspace_ccstheia/auto_car/app" -I"C:/Users/<USER>/workspace_ccstheia/auto_car/hardware" -I"C:/Users/<USER>/workspace_ccstheia/auto_car" -I"C:/Users/<USER>/workspace_ccstheia/auto_car/Debug" -I"F:/mspm0_sdk_2_05_00_05/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include" -I"F:/mspm0_sdk_2_05_00_05/mspm0_sdk_2_05_00_05/SCODE" -I"F:/mspm0_sdk_2_05_00_05/mspm0_sdk_2_05_00_05/source" -gdwarf-3 -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

build-1839431855: ../empty.syscfg
	@echo 'Building file: "$<"'
	@echo 'Invoking: SysConfig'
	"F:/CCS/ccs/utils/sysconfig_1.24.0/sysconfig_cli.bat" --script "C:/Users/<USER>/workspace_ccstheia/auto_car/empty.syscfg" -o "." -s "F:/mspm0_sdk_2_05_00_05/mspm0_sdk_2_05_00_05/.metadata/product.json" --compiler ticlang
	@echo 'Finished building: "$<"'
	@echo ' '

device_linker.cmd: build-1839431855 ../empty.syscfg
device.opt: build-1839431855
device.cmd.genlibs: build-1839431855
ti_msp_dl_config.c: build-1839431855
ti_msp_dl_config.h: build-1839431855
Event.dot: build-1839431855

%.o: ./%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"F:/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/auto_car/middle" -I"C:/Users/<USER>/workspace_ccstheia/auto_car/app" -I"C:/Users/<USER>/workspace_ccstheia/auto_car/hardware" -I"C:/Users/<USER>/workspace_ccstheia/auto_car" -I"C:/Users/<USER>/workspace_ccstheia/auto_car/Debug" -I"F:/mspm0_sdk_2_05_00_05/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include" -I"F:/mspm0_sdk_2_05_00_05/mspm0_sdk_2_05_00_05/SCODE" -I"F:/mspm0_sdk_2_05_00_05/mspm0_sdk_2_05_00_05/source" -gdwarf-3 -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

startup_mspm0g350x_ticlang.o: F:/mspm0_sdk_2_05_00_05/mspm0_sdk_2_05_00_05/source/ti/devices/msp/m0p/startup_system_files/ticlang/startup_mspm0g350x_ticlang.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"F:/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/auto_car/middle" -I"C:/Users/<USER>/workspace_ccstheia/auto_car/app" -I"C:/Users/<USER>/workspace_ccstheia/auto_car/hardware" -I"C:/Users/<USER>/workspace_ccstheia/auto_car" -I"C:/Users/<USER>/workspace_ccstheia/auto_car/Debug" -I"F:/mspm0_sdk_2_05_00_05/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include" -I"F:/mspm0_sdk_2_05_00_05/mspm0_sdk_2_05_00_05/SCODE" -I"F:/mspm0_sdk_2_05_00_05/mspm0_sdk_2_05_00_05/source" -gdwarf-3 -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


